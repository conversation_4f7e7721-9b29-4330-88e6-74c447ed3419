// Site Settings Manager
// This file handles loading and applying site settings from Supabase

// Get Supabase client using shared configuration (singleton pattern)
function getSupabaseClient() {
    // Use shared configuration if available
    if (window.SupabaseConfig) {
        return window.SupabaseConfig.getClient();
    }

    // Fallback: use global client if available
    if (window.globalSupabaseClient) {
        return window.globalSupabaseClient;
    }

    // Last resort: create client directly (should not happen if supabase-config.js is loaded)
    if (typeof window !== 'undefined' && window.supabase && typeof window.supabase.createClient === 'function') {
        console.warn('⚠️ Creating Supabase client directly - supabase-config.js may not be loaded');
        const SUPABASE_URL = 'https://krqijjttwllohulmdwgs.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtycWlqanR0d2xsb2h1bG1kd2dzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4MDM4NTEsImV4cCI6MjA2NDM3OTg1MX0.E35EsJby1Y23hnTkwHt3lREAfH-nNKNt4PZtct5QI70';

        const client = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
            global: {
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            }
        });
        window.globalSupabaseClient = client;
        return client;
    }

    console.warn('⚠️ Supabase library not loaded yet');
    return null;
}

// Global settings object
let siteSettings = {};

// Real-time subscription for settings changes
let settingsSubscription = null;

// Initialize the site settings manager object early
window.siteSettingsManager = {
    loaded: false,
    loading: false,
    loadSiteSettings: null, // Will be set later
    loadDeliverySettings: null, // Will be set later
    getSetting: null, // Will be set later
    getDeliveryFee: null, // Will be set later
    getFreeDeliveryThreshold: null, // Will be set later
    qualifiesForFreeDelivery: null, // Will be set later
    getDeliveryFeeText: null, // Will be set later
    siteSettings: () => siteSettings,
    setupRealTimeSubscription: null, // Will be set later
    cleanupSubscription: null // Will be set later
};

// Load settings from database
let siteSettingsLoading = false;
async function loadSiteSettings() {
    // Prevent multiple simultaneous calls
    if (siteSettingsLoading) {
        console.log('🔄 Site settings already loading, skipping...');
        return;
    }

    try {
        siteSettingsLoading = true;
        console.log('🔄 Starting site settings load...');
        console.log('📍 Called from:', new Error().stack.split('\n')[2]);

        // Set loading flag
        if (window.siteSettingsManager) {
            window.siteSettingsManager.loading = true;
        }

        // Get Supabase client safely
        const client = getSupabaseClient();
        if (!client) {
            console.warn('Supabase client not available, using default settings');
            setDefaultSettings();
            applySiteSettings();

            // Mark as loaded with defaults
            if (window.siteSettingsManager) {
                window.siteSettingsManager.loaded = true;
                window.siteSettingsManager.loading = false;
            }

            // Still dispatch event for default settings
            window.dispatchEvent(new CustomEvent('siteSettingsLoaded', {
                detail: { settings: siteSettings, usingDefaults: true }
            }));
            return;
        }

        // Load basic site settings
        const { data: settings, error } = await client
            .from('site_settings')
            .select('setting_key, setting_value');

        if (error) {
            console.error('Database error loading site settings:', error);
            console.log('🔄 Falling back to default settings due to database error');
            setDefaultSettings();
            applySiteSettings();

            // Mark as loaded with defaults
            if (window.siteSettingsManager) {
                window.siteSettingsManager.loaded = true;
                window.siteSettingsManager.loading = false;
            }

            // Still dispatch event for default settings
            window.dispatchEvent(new CustomEvent('siteSettingsLoaded', {
                detail: { settings: siteSettings, usingDefaults: true, error: error }
            }));
            return;
        }

        // Convert array to object for easier access
        siteSettings = {};
        if (settings && Array.isArray(settings)) {
            console.log(`📊 Loading ${settings.length} settings from database...`);
            settings.forEach(setting => {
                siteSettings[setting.setting_key] = setting.setting_value;
            });
        }

        // Load advanced delivery settings from system_settings
        await loadDeliverySettings();

        // Apply settings to the current page
        applySiteSettings();

        // Setup real-time subscription if not already done
        if (!settingsSubscription) {
            setupRealTimeSubscription();
        }

        console.log('✅ Site settings loaded successfully:', Object.keys(siteSettings).length, 'settings');

        // Log homepage-specific settings for debugging
        const homepageSettings = Object.keys(siteSettings).filter(key =>
            key.startsWith('hero_') ||
            key.startsWith('trust_') ||
            key.startsWith('featured_product') ||
            key.startsWith('features_') ||
            key.startsWith('feature_') ||
            key.startsWith('about_') ||
            key.startsWith('stat_') ||
            key.startsWith('timeline_') ||
            key.startsWith('testimonial_') ||
            key.startsWith('newsletter_') ||
            key.startsWith('categories_') ||
            key.startsWith('category_')
        );

        if (homepageSettings.length > 0) {
            console.log('🏠 Homepage settings found:', homepageSettings.length);
            homepageSettings.forEach(key => {
                console.log(`   ${key}: ${String(siteSettings[key]).substring(0, 100)}${String(siteSettings[key]).length > 100 ? '...' : ''}`);
            });
        } else {
            console.warn('⚠️ No homepage settings found in database');
        }

        // Mark as loaded and notify other scripts
        if (window.siteSettingsManager) {
            window.siteSettingsManager.loaded = true;
            window.siteSettingsManager.loading = false;
        }

        window.dispatchEvent(new CustomEvent('siteSettingsLoaded', {
            detail: { settings: siteSettings, usingDefaults: false }
        }));

    } catch (error) {
        console.error('❌ Error loading site settings:', error);

        // Use default settings if loading fails
        setDefaultSettings();
        applySiteSettings();

        // Mark as loaded with defaults
        if (window.siteSettingsManager) {
            window.siteSettingsManager.loaded = true;
            window.siteSettingsManager.loading = false;
        }

        // Dispatch event with error info
        window.dispatchEvent(new CustomEvent('siteSettingsLoaded', {
            detail: { settings: siteSettings, usingDefaults: true, error: error.message }
        }));
    } finally {
        siteSettingsLoading = false;
    }
}

// Setup real-time subscription for settings changes
function setupRealTimeSubscription() {
    const client = getSupabaseClient();
    if (!client) {
        console.warn('Cannot setup real-time subscription: Supabase client not available');
        return;
    }

    try {
        settingsSubscription = client
            .channel('site_settings_realtime')
            .on('postgres_changes',
                {
                    event: '*',
                    schema: 'public',
                    table: 'site_settings'
                },
                (payload) => {
                    console.log('Site settings changed:', payload);

                    // Update local settings based on the change
                    if (payload.eventType === 'UPDATE' || payload.eventType === 'INSERT') {
                        siteSettings[payload.new.setting_key] = payload.new.setting_value;
                    } else if (payload.eventType === 'DELETE') {
                        delete siteSettings[payload.old.setting_key];
                    }

                    // Re-apply settings to the page
                    applySiteSettings();

                    // Trigger custom event for other scripts
                    window.dispatchEvent(new CustomEvent('siteSettingsUpdated', {
                        detail: {
                            settings: siteSettings,
                            change: payload
                        }
                    }));
                }
            )
            .subscribe();
    } catch (error) {
        console.error('Error setting up real-time subscription:', error);
    }
}

// Load delivery settings from system_settings table
let deliverySettingsLoading = false;
async function loadDeliverySettings() {
    // Prevent multiple simultaneous calls
    if (deliverySettingsLoading) {
        console.log('🚚 Delivery settings already loading, skipping...');
        return;
    }

    // Clear any potential cached queries
    if (window.globalSupabaseClient && window.globalSupabaseClient.removeAllChannels) {
        try {
            window.globalSupabaseClient.removeAllChannels();
        } catch (e) {
            // Ignore errors in cleanup
        }
    }

    try {
        deliverySettingsLoading = true;
        console.log('🚚 Loading delivery settings from system_settings...');
        const client = getSupabaseClient();
        if (!client) {
            console.warn('❌ Cannot load delivery settings: Supabase client not available');
            return;
        }

        // Add network request interceptor to debug the actual URL
        const originalFetch = window.fetch;
        let interceptedUrl = null;
        window.fetch = function(...args) {
            const url = args[0];
            if (typeof url === 'string' && url.includes('system_settings')) {
                interceptedUrl = url;
                console.log('🌐 Intercepted system_settings request URL:', url);
            }
            return originalFetch.apply(this, args);
        };

        console.log('🔍 Making query to system_settings for delivery_settings...');

        // Create the query step by step to debug
        // Ensure we're using a clean string without any potential suffixes
        const settingName = 'delivery_settings';
        console.log('🔍 Using setting name:', JSON.stringify(settingName));

        // Fix: Remove .single() to avoid :1 suffix and add proper headers
        const query = client
            .from('system_settings')
            .select('setting_value')
            .eq('setting_name', settingName)
            .limit(1);

        // Log the query details
        console.log('🔍 Query object:', query);
        console.log('🔍 Query URL (if available):', query.url || 'URL not accessible');

        // Execute the query with proper error handling
        const { data: deliveryDataArray, error } = await query;

        // Extract single result from array to maintain compatibility
        const deliveryData = deliveryDataArray && deliveryDataArray.length > 0 ? deliveryDataArray[0] : null;

        // Restore original fetch and log intercepted URL
        window.fetch = originalFetch;
        if (interceptedUrl) {
            console.log('🔍 Final intercepted URL:', interceptedUrl);
            // Check if the URL contains the problematic :1 suffix
            if (interceptedUrl.includes('delivery_settings:1')) {
                console.error('❌ FOUND THE PROBLEM: URL contains delivery_settings:1');
                console.error('❌ Malformed URL:', interceptedUrl);
            }
        }

        if (error && error.code !== 'PGRST116') {
            console.warn('❌ Error loading delivery settings:', error);
            console.warn('Error details:', {
                code: error.code,
                message: error.message,
                details: error.details,
                hint: error.hint
            });

            // If we get a 406 error, try direct fetch as fallback
            if (error.message && (error.message.includes('406') || error.message.includes('Not Acceptable'))) {
                console.log('🔄 Attempting direct fetch fallback for 406 error...');
                try {
                    const directUrl = 'https://krqijjttwllohulmdwgs.supabase.co/rest/v1/system_settings?select=setting_value&setting_name=eq.delivery_settings&limit=1';
                    console.log('🌐 Direct fetch URL:', directUrl);

                    const response = await fetch(directUrl, {
                        headers: {
                            'apikey': SUPABASE_ANON_KEY,
                            'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
                            'Content-Type': 'application/json',
                            'Accept': 'application/json',
                            'Prefer': 'return=representation'
                        }
                    });

                    if (response.ok) {
                        const data = await response.json();
                        if (data && data.length > 0) {
                            console.log('✅ Direct fetch successful, using fallback data');
                            const deliverySettings = data[0].setting_value;

                            // Update delivery settings in siteSettings
                            if (deliverySettings.baghdad_fee !== undefined) {
                                siteSettings.delivery_baghdad = deliverySettings.baghdad_fee.toString();
                                console.log('📍 Baghdad delivery fee set to:', siteSettings.delivery_baghdad);
                            }
                            if (deliverySettings.other_provinces_fee !== undefined) {
                                siteSettings.delivery_other = deliverySettings.other_provinces_fee.toString();
                                console.log('🏙️ Other provinces delivery fee set to:', siteSettings.delivery_other);
                            }
                            if (deliverySettings.free_delivery_threshold !== undefined) {
                                siteSettings.free_delivery_threshold = deliverySettings.free_delivery_threshold.toString();
                                console.log('🆓 Free delivery threshold set to:', siteSettings.free_delivery_threshold);
                            }

                            console.log('✅ Delivery settings loaded successfully via direct fetch');
                            return;
                        }
                    }
                } catch (fetchError) {
                    console.error('❌ Direct fetch also failed:', fetchError);
                }
            }

            return;
        }

        if (error && error.code === 'PGRST116') {
            console.log('ℹ️ No delivery_settings found in system_settings (PGRST116 - not found)');
            return;
        }

        if (deliveryData && deliveryData.setting_value) {
            const deliverySettings = deliveryData.setting_value;
            console.log('✅ Delivery settings found:', deliverySettings);

            // Update delivery settings in siteSettings
            if (deliverySettings.baghdad_fee !== undefined) {
                siteSettings.delivery_baghdad = deliverySettings.baghdad_fee.toString();
                console.log('📍 Baghdad delivery fee set to:', siteSettings.delivery_baghdad);
            }
            if (deliverySettings.other_provinces_fee !== undefined) {
                siteSettings.delivery_other = deliverySettings.other_provinces_fee.toString();
                console.log('🏙️ Other provinces delivery fee set to:', siteSettings.delivery_other);
            }
            if (deliverySettings.free_delivery_threshold !== undefined) {
                siteSettings.free_delivery_threshold = deliverySettings.free_delivery_threshold.toString();
                console.log('🆓 Free delivery threshold set to:', siteSettings.free_delivery_threshold);
            }

            console.log('✅ Delivery settings loaded successfully');
        } else {
            console.log('ℹ️ No delivery data found or setting_value is empty');
        }
    } catch (error) {
        console.error('❌ Failed to load delivery settings (catch block):', error);
        console.error('Error stack:', error.stack);
    } finally {
        deliverySettingsLoading = false;
    }
}

// Set default settings as fallback
function setDefaultSettings() {
    siteSettings = {
        business_name: 'Care',
        business_phone: '***********',
        business_email: '<EMAIL>',
        business_address: 'الكرادة، قرب مطعم المحطة',
        working_days: 'السبت - الخميس',
        working_hours: '10 صباحاً - 5 مساءً',
        closed_day: 'الجمعة',
        about_title: 'نبذة عن متجر Care',
        about_description: 'متجر Care هو وجهتك المثالية للحصول على أفضل منتجات العناية بالبشرة والشعر. نحن نقدم منتجات عالية الجودة من أفضل العلامات التجارية العالمية.',
        // Store overview settings (from admin panel)
        store_overview_title: 'نبذة عن متجر Care',
        store_overview_description: 'متجر Care هو وجهتك المثالية للحصول على أفضل منتجات العناية بالبشرة والشعر عالية الجودة من أفضل العلامات التجارية العالمية.',
        whatsapp_number: '9647713688302',

        facebook_url: '',
        instagram_url: '',
        telegram_url: '',
        tiktok_url: '',
        youtube_url: '',
        twitter_url: '',
        linkedin_url: '',
        snapchat_url: '',
        // Delivery settings
        delivery_baghdad: '5000',
        delivery_other: '10000',
        free_delivery_threshold: '50000',
        // Hero section
        hero_tagline: 'أفضل منتجات العناية بالبشرة والشعر في العراق - جودة عالية وأسعار مناسبة',
        // Banner background settings
        banner_background_enabled: '0',
        banner_background_url: '',
        // Trust indicators
        trust_indicator_1: 'منتجات أصلية 100%',
        trust_indicator_1_icon: 'fas fa-certificate',
        trust_indicator_2: 'توصيل مجاني للطلبات +50 ألف',
        trust_indicator_2_icon: 'fas fa-shipping-fast',
        trust_indicator_3: 'ضمان الاسترداد 30 يوم',
        trust_indicator_3_icon: 'fas fa-undo-alt',
        // Store statistics
        store_products_count: '500+',
        store_satisfied_customers: '1000+',
        store_experience_years: '5',
        store_customer_support: '24/7',
        // Why Choose Care section
        why_choose_title: 'لماذا تختار Care؟',
        why_choose_description: 'نحن نقدم أفضل تجربة تسوق للعناية والجمال مع ضمان الجودة والأسعار المناسبة',
        // Features
        feature_1_title: 'جودة عالية',
        feature_1_description: 'نختار منتجاتنا بعناية فائقة من أفضل العلامات التجارية العالمية',
        feature_1_icon: 'fas fa-star',
        feature_2_title: 'أسعار منافسة',
        feature_2_description: 'نقدم أفضل الأسعار في السوق مع عروض وخصومات مستمرة',
        feature_2_icon: 'fas fa-tags',
        feature_3_title: 'توصيل سريع',
        feature_3_description: 'خدمة توصيل سريعة وآمنة لجميع محافظات العراق',
        feature_3_icon: 'fas fa-shipping-fast',
        feature_4_title: 'دعم متميز',
        feature_4_description: 'فريق خدمة عملاء محترف متاح 24/7 لمساعدتك',
        feature_4_icon: 'fas fa-headset',
        feature_5_title: 'ضمان الاسترداد',
        feature_5_description: 'إمكانية إرجاع المنتج خلال 30 يوم مع ضمان استرداد كامل للمبلغ دون أي شروط معقدة',
        feature_5_icon: 'fas fa-undo-alt',
        feature_6_title: 'استشارة مجانية',
        feature_6_description: 'استشارة مجانية من خبراء العناية المعتمدين لاختيار المنتجات الأنسب لنوع بشرتك وشعرك',
        feature_6_icon: 'fas fa-user-md',
        // Featured products section
        featured_products_title: 'منتجاتنا المميزة',
        featured_products_description: 'اكتشف مجموعتنا المختارة بعناية من أفضل منتجات العناية بالبشرة والشعر',
        // About section
        about_badge: 'قصتنا',
        about_title: 'من نحن',
        about_subtitle: 'رحلتنا في عالم الجمال والعناية بدأت من شغف حقيقي بتقديم الأفضل لعملائنا الكرام في جميع أنحاء العراق',
        about_description_1: 'منذ تأسيسنا عام 2018، نسعى لتقديم أفضل منتجات العناية بالبشرة والشعر من أرقى العلامات التجارية العالمية. نؤمن بأن الجمال الحقيقي يبدأ من العناية الصحيحة والمنتجات عالية الجودة.',
        about_description_2: 'فريقنا المتخصص يعمل بجد لاختيار كل منتج بعناية فائقة، مع التأكد من أصالته وجودته العالية. نحن لسنا مجرد متجر، بل شريكك الموثوق في رحلة العناية والجمال.',
        // Page descriptions
        products_page_description: 'اكتشف مجموعتنا الكاملة من منتجات العناية بالبشرة والشعر',

        // Timeline section settings
        timeline_enabled: '1',
        timeline_badge: 'رحلتنا',
        timeline_title: 'قصة نجاح متجر Care',
        timeline_subtitle: 'رحلة من الشغف إلى التميز في عالم الجمال والعناية',
        timeline_stat_years: '6+',
        timeline_stat_customers: '5000+',
        timeline_stat_products: '500+',
        timeline_stat_brands: '50+',
        timeline_stats_enabled: '1',
        // Timeline individual items
        timeline_1_enabled: '1',
        timeline_2_enabled: '1',
        timeline_3_enabled: '1',
        timeline_4_enabled: '1',
        timeline_5_enabled: '1',
        timeline_6_enabled: '1',

        // Benefits showcase section settings
        benefits_showcase_enabled: '1',
        benefits_showcase_badge: 'فوائد منتجاتنا',
        benefits_showcase_title: 'اكتشف الفوائد المذهلة لمنتجاتنا',
        benefits_showcase_subtitle: 'منتجات مصممة خصيصاً لتلبية احتياجاتك وتحقيق أفضل النتائج',

        // Benefits items (1-6)
        benefit_1_title: 'مكونات طبيعية',
        benefit_1_description: 'مستخلصات طبيعية 100% آمنة على البشرة ومناسبة لجميع أنواع البشرة',
        benefit_1_details: 'نستخدم أفضل المكونات الطبيعية المستخلصة من النباتات العضوية المعتمدة عالمياً',
        benefit_1_stat_value: '95%',
        benefit_1_stat_label: 'مكونات طبيعية',
        benefit_1_icon: 'fas fa-leaf',

        benefit_2_title: 'مختبر علمياً',
        benefit_2_description: 'منتجات مختبرة في أفضل المختبرات العالمية لضمان الفعالية والأمان',
        benefit_2_details: 'جميع منتجاتنا تخضع لاختبارات صارمة في مختبرات معتمدة دولياً',
        benefit_2_stat_value: '100%',
        benefit_2_stat_label: 'مختبر علمياً',
        benefit_2_icon: 'fas fa-shield-check',

        benefit_3_title: 'نتائج سريعة',
        benefit_3_description: 'نتائج ملحوظة خلال أسابيع قليلة من الاستخدام المنتظم',
        benefit_3_details: 'تركيبات متطورة تضمن نتائج فعالة وسريعة مع الاستخدام المنتظم',
        benefit_3_stat_value: '2-4',
        benefit_3_stat_label: 'أسابيع للنتائج',
        benefit_3_icon: 'fas fa-clock',

        benefit_4_title: 'مناسب للبشرة الحساسة',
        benefit_4_description: 'تركيبات لطيفة وآمنة حتى للبشرة الأكثر حساسية',
        benefit_4_details: 'منتجات مصممة خصيصاً للبشرة الحساسة مع تركيبات لطيفة وآمنة',
        benefit_4_stat_value: '99%',
        benefit_4_stat_label: 'آمان للبشرة الحساسة',
        benefit_4_icon: 'fas fa-heart',

        benefit_5_title: 'حائز على جوائز',
        benefit_5_description: 'منتجات حائزة على جوائز عالمية في مجال العناية والجمال',
        benefit_5_details: 'منتجاتنا حاصلة على جوائز مرموقة من أهم المؤسسات العالمية للجمال',
        benefit_5_stat_value: '15+',
        benefit_5_stat_label: 'جائزة عالمية',
        benefit_5_icon: 'fas fa-award',

        benefit_6_title: 'صديق للبيئة',
        benefit_6_description: 'عبوات قابلة للتدوير ومكونات مستدامة بيئياً',
        benefit_6_details: 'نلتزم بحماية البيئة من خلال استخدام عبوات قابلة للتدوير ومكونات مستدامة',
        benefit_6_stat_value: '80%',
        benefit_6_stat_label: 'مواد قابلة للتدوير',
        benefit_6_icon: 'fas fa-recycle',

        // Benefits enable/disable toggles
        benefit_4_enabled: '1',
        benefit_5_enabled: '1',
        benefit_6_enabled: '1',

        // Benefits summary section
        benefits_summary_title: 'لماذا تختار منتجاتنا؟',
        benefits_summary_description: 'نحن نؤمن بأن الجمال الحقيقي يأتي من منتجات عالية الجودة وآمنة. لذلك نقدم لك مجموعة منتجات مختارة بعناية لتحقيق أفضل النتائج.',
        benefits_summary_stat1_value: '98%',
        benefits_summary_stat1_label: 'رضا العملاء',
        benefits_summary_stat2_value: '50+',
        benefits_summary_stat2_label: 'علامة تجارية',
        benefits_summary_stat3_value: '24/7',
        benefits_summary_stat3_label: 'دعم العملاء',
        offers_page_description: 'اكتشف أفضل العروض على منتجات العناية بالبشرة والشعر',
        contact_page_description: 'نحن هنا لمساعدتك والإجابة على جميع استفساراتك',
        faq_page_description: 'إجابات على أكثر الأسئلة شيوعاً حول منتجاتنا وخدماتنا',
        cart_page_description: 'راجع منتجاتك المختارة واكمل عملية الشراء',

        copyright_text: 'جميع الحقوق محفوظة',
        guidelines_page_description: 'تعلم كيفية استخدام منتجات العناية بالبشرة والشعر بالطريقة الصحيحة',
        faq_page_description: 'إجابات على أكثر الأسئلة شيوعاً حول منتجاتنا وخدماتنا',
        terms_page_description: 'يرجى قراءة الشروط والأحكام بعناية قبل استخدام خدماتنا',
        // Categories section
        categories_badge: 'تسوق حسب الفئة',
        categories_title: 'اكتشف مجموعاتنا المتنوعة',
        categories_subtitle: 'تصفح مجموعة واسعة من منتجات العناية بالبشرة والشعر المصنفة بعناية لتناسب احتياجاتك',
        category_1_enabled: '1',
        category_1_name: 'العناية بالبشرة',
        category_1_description: 'منتجات متخصصة للعناية اليومية بالبشرة',
        category_1_count: '150+ منتج',
        category_1_image: 'https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=400&h=300&fit=crop',
        category_2_enabled: '1',
        category_2_name: 'العناية بالشعر',
        category_2_description: 'شامبو وبلسم وعلاجات للشعر',
        category_2_count: '120+ منتج',
        category_2_image: 'https://images.unsplash.com/photo-1522338242992-e1a54906a8da?w=400&h=300&fit=crop',
        category_3_enabled: '1',
        category_3_name: 'المكياج',
        category_3_description: 'مستحضرات تجميل عالية الجودة',
        category_3_count: '200+ منتج',
        category_3_image: 'https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=400&h=300&fit=crop',
        category_4_enabled: '1',
        category_4_name: 'العطور',
        category_4_description: 'عطور فاخرة للرجال والنساء',
        category_4_count: '80+ منتج',
        category_4_image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',
        // Cart labels
        order_summary_label: 'ملخص الطلب',
        subtotal_label: 'المجموع الفرعي:',
        delivery_fees_label: 'رسوم التوصيل:',
        total_amount_label: 'المجموع الكلي:',
        customer_info_label: 'معلومات العميل',
        full_name_label: 'الاسم الكامل',
        phone_number_label: 'رقم الهاتف',
        full_address_label: 'العنوان الكامل',
        delivery_area_label: 'منطقة التوصيل',
        choose_delivery_label: 'اختر منطقة التوصيل',
        additional_notes_label: 'ملاحظات إضافية',
        // Section visibility settings
        timeline_enabled: '1',
        benefits_showcase_enabled: '1',
        newsletter_enabled: '1',
        testimonials_enabled: '1',
        testimonials_stats_enabled: '1',
        trust_indicators_enabled: '1',
        featured_product_enabled: '1',
        feature_1_enabled: '1',
        feature_2_enabled: '1',
        feature_3_enabled: '1',
        feature_4_enabled: '1',
        feature_5_enabled: '1',
        feature_6_enabled: '1'
    };
}

// Apply page header background images based on settings
function applyPageHeaderBackgrounds() {
    console.log('🖼️ Applying page header background images...');

    const pageConfigs = [
        { page: 'products', elementId: 'productsPageHeader', className: 'products-bg' },
        { page: 'contact', elementId: 'contactPageHeader', className: 'contact-bg' },
        { page: 'faq', elementId: 'faqPageHeader', className: 'faq-bg' },
        { page: 'guidelines', elementId: 'guidelinesPageHeader', className: 'guidelines-bg' },
        { page: 'terms', elementId: 'termsPageHeader', className: 'terms-bg' },
        { page: 'offers', elementId: 'offersPageHeader', className: 'offers-bg' }
    ];

    pageConfigs.forEach(config => {
        const headerElement = document.getElementById(config.elementId);
        if (!headerElement) return;

        const enabledKey = `${config.page}_header_bg_enabled`;
        const urlKey = `${config.page}_header_bg_url`;

        const isEnabled = siteSettings[enabledKey] === 'true';
        const bgUrl = siteSettings[urlKey];

        console.log(`🔄 Processing ${config.page} header background: enabled=${isEnabled}, url=${bgUrl ? bgUrl.substring(0, 50) + '...' : 'none'}`);

        if (isEnabled && bgUrl && bgUrl.trim() !== '') {
            // Apply background image
            headerElement.style.setProperty(`--${config.page}-bg-url`, `url("${bgUrl}")`);
            headerElement.classList.add('has-background-image');

            // Add loading state
            headerElement.classList.add('bg-loading');

            // Test if image loads successfully
            const img = new Image();
            img.onload = function() {
                headerElement.classList.remove('bg-loading', 'bg-error');
                console.log(`✅ Background image loaded successfully for ${config.page} page`);
            };
            img.onerror = function() {
                headerElement.classList.remove('bg-loading');
                headerElement.classList.add('bg-error');
                headerElement.classList.remove('has-background-image');
                console.error(`❌ Failed to load background image for ${config.page} page: ${bgUrl}`);
            };
            img.src = bgUrl;
        } else {
            // Remove background image
            headerElement.style.removeProperty(`--${config.page}-bg-url`);
            headerElement.classList.remove('has-background-image', 'bg-loading', 'bg-error');
            console.log(`🚫 Background image disabled for ${config.page} page`);
        }
    });
}

// Apply settings to the current page
function applySiteSettings() {


    // Update business name
    const businessName = siteSettings.business_name || 'Care';

    console.log('🎨 Applying site settings:', {
        businessName,
        currentPage: window.location.pathname
    });

    // Handle header logo elements (should show logo when logo mode is selected)
    // Use a more comprehensive selector to catch all header business name elements
    // Updated selector to properly target elements with both logo and business-name classes
    const headerLogoElements = document.querySelectorAll('header .logo.business-name, header .business-name, header .logo, header a[class*="business-name"], header a[class*="logo"], header [data-setting="business_name"]');
    console.log(`🏷️ Found ${headerLogoElements.length} header logo elements`);

    headerLogoElements.forEach((element, index) => {
        console.log(`🏷️ Processing header element ${index + 1}:`, element.className, element.tagName);
        // Use text-based business name for header
        element.textContent = businessName;
        element.classList.remove('logo-display-mode');
        console.log(`✅ Applied text to header element ${index + 1}`);
    });

    // Handle footer copyright - ALWAYS keep store name text, never replace with logo
    const footerBusinessNameElements = document.querySelectorAll('footer .business-name[data-setting="business_name"]');
    console.log(`📄 Found ${footerBusinessNameElements.length} footer business name elements`);

    footerBusinessNameElements.forEach((element, index) => {
        console.log(`📄 Processing footer element ${index + 1}:`, element.className);
        // Always use text for footer copyright, regardless of logo setting
        element.textContent = businessName;
        element.classList.remove('logo-display-mode');
        console.log(`✅ Applied text to footer element ${index + 1}`);
    });

    // Handle footer copyright elements (should ALWAYS show text, never logo)
    const footerCopyrightElements = document.querySelectorAll('footer .business-name, footer [data-setting="business_name"]');
    console.log(`📄 Found ${footerCopyrightElements.length} footer copyright elements`);

    footerCopyrightElements.forEach((element, index) => {
        // Footer copyright should always show text, never logo
        element.textContent = businessName;
        console.log(`✅ Applied text to footer element ${index + 1}`);
    });

    // Handle other business name elements (general case)
    const otherBusinessNameElements = document.querySelectorAll('.business-name:not(header .business-name):not(footer .business-name), [data-setting="business_name"]:not(header [data-setting="business_name"]):not(footer [data-setting="business_name"])');
    otherBusinessNameElements.forEach(element => {
        // Use text-based business name
        element.textContent = businessName;
    });

    // Update phone numbers
    const phoneElements = document.querySelectorAll('.business-phone, [data-setting="business_phone"]');
    phoneElements.forEach(element => {
        element.textContent = siteSettings.business_phone || '***********';
        if (element.tagName === 'A') {
            element.href = `tel:${siteSettings.business_phone}`;
        }
    });

    // Update email
    const emailElements = document.querySelectorAll('.business-email, [data-setting="business_email"]');
    emailElements.forEach(element => {
        element.textContent = siteSettings.business_email || '<EMAIL>';
        if (element.tagName === 'A') {
            element.href = `mailto:${siteSettings.business_email}`;
        }
    });

    // Update address
    const addressElements = document.querySelectorAll('.business-address, [data-setting="business_address"]');
    addressElements.forEach(element => {
        element.textContent = siteSettings.business_address || 'الكرادة، قرب مطعم المحطة';
    });

    // Update working hours
    const workingDaysElements = document.querySelectorAll('.working-days, [data-setting="working_days"]');
    workingDaysElements.forEach(element => {
        element.textContent = siteSettings.working_days || 'السبت - الخميس';
    });

    const workingHoursElements = document.querySelectorAll('.working-hours, [data-setting="working_hours"]');
    workingHoursElements.forEach(element => {
        element.textContent = siteSettings.working_hours || '10 صباحاً - 5 مساءً';
    });

    const closedDayElements = document.querySelectorAll('.closed-day, [data-setting="closed_day"]');
    closedDayElements.forEach(element => {
        element.textContent = siteSettings.closed_day || 'الجمعة';
    });

    // Update about section (support both about_* and store_overview_* keys)
    const aboutTitleElements = document.querySelectorAll('.about-title, [data-setting="about_title"], [data-setting="store_overview_title"]');
    aboutTitleElements.forEach(element => {
        // Prefer store_overview_title from admin panel, fallback to about_title, then default
        const titleText = siteSettings.store_overview_title || siteSettings.about_title || 'نبذة عن متجر Care';
        element.textContent = titleText;
        console.log('🔄 Updated about title:', titleText);
    });

    const aboutDescriptionElements = document.querySelectorAll('.about-description, [data-setting="about_description"], [data-setting="store_overview_description"]');
    aboutDescriptionElements.forEach(element => {
        // Prefer store_overview_description from admin panel, fallback to about_description, then default
        const descriptionText = siteSettings.store_overview_description || siteSettings.about_description || 'متجر Care هو وجهتك المثالية للحصول على أفضل منتجات العناية بالبشرة والشعر.';
        element.textContent = descriptionText;
        console.log('🔄 Updated about description:', descriptionText.substring(0, 100) + '...');
    });

    // Update social media links
    const whatsappElements = document.querySelectorAll('.whatsapp-link, [data-setting="whatsapp_link"]');
    whatsappElements.forEach(element => {
        if (siteSettings.whatsapp_number) {
            element.href = `https://wa.me/${siteSettings.whatsapp_number}`;
            element.style.display = '';
        } else {
            element.style.display = 'none';
        }
    });

    const facebookElements = document.querySelectorAll('.facebook-link, [data-setting="facebook_link"]');
    facebookElements.forEach(element => {
        if (siteSettings.facebook_url) {
            element.href = siteSettings.facebook_url;
            element.style.display = '';
        } else {
            element.style.display = 'none';
        }
    });

    const instagramElements = document.querySelectorAll('.instagram-link, [data-setting="instagram_link"]');
    instagramElements.forEach(element => {
        if (siteSettings.instagram_url) {
            element.href = siteSettings.instagram_url;
            element.style.display = '';
        } else {
            element.style.display = 'none';
        }
    });

    // Update telegram links
    const telegramElements = document.querySelectorAll('.telegram-link, [data-setting="telegram_link"]');
    telegramElements.forEach(element => {
        if (siteSettings.telegram_url) {
            element.href = siteSettings.telegram_url;
            element.style.display = '';
        } else {
            element.style.display = 'none';
        }
    });

    // Update tiktok links
    const tiktokElements = document.querySelectorAll('.tiktok-link, [data-setting="tiktok_link"]');
    tiktokElements.forEach(element => {
        if (siteSettings.tiktok_url) {
            element.href = siteSettings.tiktok_url;
            element.style.display = '';
        } else {
            element.style.display = 'none';
        }
    });

    // Update youtube links
    const youtubeElements = document.querySelectorAll('.youtube-link, [data-setting="youtube_link"]');
    youtubeElements.forEach(element => {
        if (siteSettings.youtube_url) {
            element.href = siteSettings.youtube_url;
            element.style.display = '';
        } else {
            element.style.display = 'none';
        }
    });

    // Update twitter links
    const twitterElements = document.querySelectorAll('.twitter-link, [data-setting="twitter_link"]');
    twitterElements.forEach(element => {
        if (siteSettings.twitter_url) {
            element.href = siteSettings.twitter_url;
            element.style.display = '';
        } else {
            element.style.display = 'none';
        }
    });

    // Update linkedin links
    const linkedinElements = document.querySelectorAll('.linkedin-link, [data-setting="linkedin_link"]');
    linkedinElements.forEach(element => {
        if (siteSettings.linkedin_url) {
            element.href = siteSettings.linkedin_url;
            element.style.display = '';
        } else {
            element.style.display = 'none';
        }
    });

    // Update snapchat links
    const snapchatElements = document.querySelectorAll('.snapchat-link, [data-setting="snapchat_link"]');
    snapchatElements.forEach(element => {
        if (siteSettings.snapchat_url) {
            element.href = siteSettings.snapchat_url;
            element.style.display = '';
        } else {
            element.style.display = 'none';
        }
    });

    // Update WhatsApp order links with the correct number
    const whatsappOrderElements = document.querySelectorAll('[data-whatsapp-order]');
    whatsappOrderElements.forEach(element => {
        if (siteSettings.whatsapp_number) {
            const currentHref = element.getAttribute('href') || '';
            const newHref = currentHref.replace(/wa\.me\/\d+/, `wa.me/${siteSettings.whatsapp_number}`);
            element.href = newHref;
        }
    });

    // Update hero tagline
    const heroTaglineElements = document.querySelectorAll('.hero-tagline, [data-setting="hero_tagline"]');
    heroTaglineElements.forEach(element => {
        element.textContent = siteSettings.hero_tagline || 'أفضل منتجات العناية بالبشرة والشعر في العراق - جودة عالية وأسعار مناسبة';
    });

    // Update store statistics
    const productsCountElements = document.querySelectorAll('.store-products-count, [data-setting="store_products_count"]');
    productsCountElements.forEach(element => {
        element.textContent = siteSettings.store_products_count || '500+';
    });

    const customersCountElements = document.querySelectorAll('.store-customers-count, [data-setting="store_satisfied_customers"]');
    customersCountElements.forEach(element => {
        element.textContent = siteSettings.store_satisfied_customers || '1000+';
    });

    const experienceYearsElements = document.querySelectorAll('.store-experience-years, [data-setting="store_experience_years"]');
    experienceYearsElements.forEach(element => {
        element.textContent = siteSettings.store_experience_years || '5';
    });

    const customerSupportElements = document.querySelectorAll('.store-customer-support, [data-setting="store_customer_support"]');
    customerSupportElements.forEach(element => {
        element.textContent = siteSettings.store_customer_support || '24/7';
    });

    // Update "Why Choose Care?" section
    const whyChooseTitleElements = document.querySelectorAll('.why-choose-title, [data-setting="why_choose_title"]');
    whyChooseTitleElements.forEach(element => {
        element.textContent = siteSettings.why_choose_title || 'لماذا تختار Care؟';
    });

    const whyChooseDescriptionElements = document.querySelectorAll('.why-choose-description, [data-setting="why_choose_description"]');
    whyChooseDescriptionElements.forEach(element => {
        element.textContent = siteSettings.why_choose_description || 'نحن نقدم أفضل تجربة تسوق للعناية والجمال مع ضمان الجودة والأسعار المناسبة';
    });

    // Update features
    for (let i = 1; i <= 6; i++) {
        const featureTitleElements = document.querySelectorAll(`.feature-${i}-title, [data-setting="feature_${i}_title"]`);
        featureTitleElements.forEach(element => {
            element.textContent = siteSettings[`feature_${i}_title`] || '';
        });

        const featureDescriptionElements = document.querySelectorAll(`.feature-${i}-description, [data-setting="feature_${i}_description"]`);
        featureDescriptionElements.forEach(element => {
            element.textContent = siteSettings[`feature_${i}_description`] || '';
        });

        const featureIconElements = document.querySelectorAll(`.feature-${i}-icon, [data-setting="feature_${i}_icon"]`);
        featureIconElements.forEach(element => {
            const iconClass = siteSettings[`feature_${i}_icon`] || 'fas fa-star';
            element.className = iconClass;
        });

        // Individual feature visibility control
        const featureCards = document.querySelectorAll(`.feature-card:nth-child(${i})`);
        featureCards.forEach(card => {
            const featureEnabled = siteSettings[`feature_${i}_enabled`] === '1' || siteSettings[`feature_${i}_enabled`] === true || siteSettings[`feature_${i}_enabled`] === undefined;
            card.style.display = featureEnabled ? 'block' : 'none';
            console.log(`🎯 Feature ${i} ${featureEnabled ? 'enabled' : 'disabled'} (setting value: ${siteSettings[`feature_${i}_enabled`]})`);
        });
    }

    // Update featured products section
    const featuredTitleElements = document.querySelectorAll('.featured-products-title, [data-setting="featured_products_title"]');
    featuredTitleElements.forEach(element => {
        element.textContent = siteSettings.featured_products_title || 'منتجاتنا المميزة';
    });

    const featuredDescriptionElements = document.querySelectorAll('.featured-products-description, [data-setting="featured_products_description"]');
    featuredDescriptionElements.forEach(element => {
        element.textContent = siteSettings.featured_products_description || 'اكتشف مجموعتنا المختارة بعناية من أفضل منتجات العناية بالبشرة والشعر';
    });

    // Update categories section
    const categoriesBadgeElements = document.querySelectorAll('[data-setting="categories_badge"]');
    categoriesBadgeElements.forEach(element => {
        element.textContent = siteSettings.categories_badge || 'تسوق حسب الفئة';
    });

    const categoriesTitleElements = document.querySelectorAll('[data-setting="categories_title"]');
    categoriesTitleElements.forEach(element => {
        element.textContent = siteSettings.categories_title || 'اكتشف مجموعاتنا المتنوعة';
    });

    const categoriesSubtitleElements = document.querySelectorAll('[data-setting="categories_subtitle"]');
    categoriesSubtitleElements.forEach(element => {
        element.textContent = siteSettings.categories_subtitle || 'تصفح مجموعة واسعة من منتجات العناية بالبشرة والشعر المصنفة بعناية لتناسب احتياجاتك';
    });

    // Update individual categories
    for (let i = 1; i <= 4; i++) {
        const categoryNameElements = document.querySelectorAll(`[data-setting="category_${i}_name"]`);
        categoryNameElements.forEach(element => {
            element.textContent = siteSettings[`category_${i}_name`] || '';
        });

        const categoryDescriptionElements = document.querySelectorAll(`[data-setting="category_${i}_description"]`);
        categoryDescriptionElements.forEach(element => {
            element.textContent = siteSettings[`category_${i}_description`] || '';
        });

        const categoryCountElements = document.querySelectorAll(`[data-setting="category_${i}_count"]`);
        categoryCountElements.forEach(element => {
            element.textContent = siteSettings[`category_${i}_count`] || '';
        });

        const categoryImageElements = document.querySelectorAll(`[data-setting="category_${i}_image"]`);
        categoryImageElements.forEach(element => {
            if (element.tagName === 'IMG' && siteSettings[`category_${i}_image`]) {
                element.src = siteSettings[`category_${i}_image`];
            }
        });

        // Handle category visibility based on enabled status
        const categoryCards = document.querySelectorAll(`.category-card:nth-child(${i})`);
        categoryCards.forEach(card => {
            if (siteSettings[`category_${i}_enabled`] === '0') {
                card.style.display = 'none';
            } else {
                card.style.display = '';
            }
        });
    }

    // Update testimonials section
    const testimonialsBadgeElements = document.querySelectorAll('[data-setting="testimonials_badge"]');
    testimonialsBadgeElements.forEach(element => {
        element.textContent = siteSettings.testimonials_badge || 'آراء عملائنا';
    });

    const testimonialsTitleElements = document.querySelectorAll('[data-setting="testimonials_title"]');
    testimonialsTitleElements.forEach(element => {
        element.textContent = siteSettings.testimonials_title || 'ماذا يقول عملاؤنا عنا؟';
    });

    const testimonialsSubtitleElements = document.querySelectorAll('[data-setting="testimonials_subtitle"]');
    testimonialsSubtitleElements.forEach(element => {
        element.textContent = siteSettings.testimonials_subtitle || 'تجارب حقيقية من عملائنا الكرام الذين جربوا منتجاتنا وخدماتنا المتميزة';
    });

    // Update individual testimonials (support up to 10 testimonials)
    for (let i = 1; i <= 10; i++) {
        const testimonialNameElements = document.querySelectorAll(`[data-setting="testimonial_${i}_name"]`);
        testimonialNameElements.forEach(element => {
            element.textContent = siteSettings[`testimonial_${i}_name`] || '';
        });

        const testimonialLocationElements = document.querySelectorAll(`[data-setting="testimonial_${i}_location"]`);
        testimonialLocationElements.forEach(element => {
            element.textContent = siteSettings[`testimonial_${i}_location`] || '';
        });

        const testimonialInitialElements = document.querySelectorAll(`[data-setting="testimonial_${i}_initial"]`);
        testimonialInitialElements.forEach(element => {
            element.textContent = siteSettings[`testimonial_${i}_initial`] || '';
        });

        const testimonialRatingElements = document.querySelectorAll(`[data-setting="testimonial_${i}_rating"]`);
        testimonialRatingElements.forEach(element => {
            element.textContent = siteSettings[`testimonial_${i}_rating`] || '5.0';
        });

        const testimonialTextElements = document.querySelectorAll(`[data-setting="testimonial_${i}_text"]`);
        testimonialTextElements.forEach(element => {
            element.textContent = siteSettings[`testimonial_${i}_text`] || '';
        });

        // Handle testimonial images/avatars
        const testimonialAvatarElements = document.querySelectorAll(`[data-setting="testimonial_${i}_avatar"]`);
        testimonialAvatarElements.forEach(element => {
            const imageUrl = siteSettings[`testimonial_${i}_image`];
            const initial = siteSettings[`testimonial_${i}_initial`] || '';

            if (imageUrl && imageUrl.trim() !== '') {
                // Use customer image
                if (element.tagName === 'IMG') {
                    element.src = imageUrl;
                    element.alt = `صورة ${siteSettings[`testimonial_${i}_name`] || 'العميل'}`;
                    element.style.display = 'block';
                } else {
                    element.innerHTML = `<img src="${imageUrl}" alt="صورة ${siteSettings[`testimonial_${i}_name`] || 'العميل'}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 50%;">`;
                    // Ensure the container maintains its enhanced styling
                    element.style.boxShadow = '';
                    element.style.border = '';
                }
            } else {
                // Use text-based avatar with initial - preserve the span element
                if (element.tagName === 'IMG') {
                    element.style.display = 'none';
                } else {
                    // Find the existing span element or create one
                    let spanElement = element.querySelector(`[data-setting="testimonial_${i}_initial"]`);
                    if (!spanElement) {
                        spanElement = document.createElement('span');
                        spanElement.setAttribute('data-setting', `testimonial_${i}_initial`);
                        element.innerHTML = '';
                        element.appendChild(spanElement);
                    }
                    spanElement.textContent = initial;

                    // Reset avatar container styles to default while preserving enhanced styling
                    element.style.display = 'flex';
                    element.style.alignItems = 'center';
                    element.style.justifyContent = 'center';
                    // Remove inline styles to let CSS classes handle styling
                    element.style.backgroundColor = '';
                    element.style.color = '';
                    element.style.fontWeight = '';
                    element.style.fontSize = '';
                    element.style.borderRadius = '';
                    element.style.boxShadow = '';
                    element.style.border = '';
                }
            }
        });
    }

    // Update testimonials statistics
    const testimonialsTotalReviewsElements = document.querySelectorAll('[data-setting="testimonials_total_reviews"]');
    testimonialsTotalReviewsElements.forEach(element => {
        element.textContent = siteSettings.testimonials_total_reviews || '2,500+';
    });

    const testimonialsAverageRatingElements = document.querySelectorAll('[data-setting="testimonials_average_rating"]');
    testimonialsAverageRatingElements.forEach(element => {
        element.textContent = siteSettings.testimonials_average_rating || '4.9';
    });

    const testimonialsSatisfactionElements = document.querySelectorAll('[data-setting="testimonials_satisfaction"]');
    testimonialsSatisfactionElements.forEach(element => {
        element.textContent = siteSettings.testimonials_satisfaction || '98%';
    });

    // Update page descriptions
    const productsPageDescElements = document.querySelectorAll('.products-page-description, [data-setting="products_page_description"]');
    productsPageDescElements.forEach(element => {
        element.textContent = siteSettings.products_page_description || 'اكتشف مجموعتنا الكاملة من منتجات العناية بالبشرة والشعر';
    });

    const offersPageDescElements = document.querySelectorAll('.offers-page-description, [data-setting="offers_page_description"]');
    offersPageDescElements.forEach(element => {
        element.textContent = siteSettings.offers_page_description || 'اكتشف أفضل العروض على منتجات العناية بالبشرة والشعر';
    });

    const contactPageDescElements = document.querySelectorAll('.contact-page-description, [data-setting="contact_page_description"]');
    contactPageDescElements.forEach(element => {
        element.textContent = siteSettings.contact_page_description || 'نحن هنا لمساعدتك والإجابة على جميع استفساراتك';
    });

    const cartPageDescElements = document.querySelectorAll('.cart-page-description, [data-setting="cart_page_description"]');
    cartPageDescElements.forEach(element => {
        element.textContent = siteSettings.cart_page_description || 'راجع منتجاتك المختارة واكمل عملية الشراء';
    });



    const copyrightTextElements = document.querySelectorAll('.copyright-text, [data-setting="copyright_text"]');
    copyrightTextElements.forEach(element => {
        element.textContent = siteSettings.copyright_text || 'جميع الحقوق محفوظة';
    });

    // Update guidelines, FAQ, and terms page descriptions
    const guidelinesPageDescElements = document.querySelectorAll('.guidelines-page-description, [data-setting="guidelines_page_description"]');
    guidelinesPageDescElements.forEach(element => {
        element.textContent = siteSettings.guidelines_page_description || 'تعلم كيفية استخدام منتجات العناية بالبشرة والشعر بالطريقة الصحيحة';
    });

    const faqPageDescElements2 = document.querySelectorAll('.faq-page-description, [data-setting="faq_page_description"]');
    faqPageDescElements2.forEach(element => {
        element.textContent = siteSettings.faq_page_description || 'إجابات على أكثر الأسئلة شيوعاً حول منتجاتنا وخدماتنا';
    });

    const termsPageDescElements = document.querySelectorAll('.terms-page-description, [data-setting="terms_page_description"]');
    termsPageDescElements.forEach(element => {
        element.textContent = siteSettings.terms_page_description || 'يرجى قراءة الشروط والأحكام بعناية قبل استخدام خدماتنا';
    });

    // Update cart labels
    const orderSummaryElements = document.querySelectorAll('.order-summary-label, [data-setting="order_summary_label"]');
    orderSummaryElements.forEach(element => {
        element.textContent = siteSettings.order_summary_label || 'ملخص الطلب';
    });

    const subtotalElements = document.querySelectorAll('.subtotal-label, [data-setting="subtotal_label"]');
    subtotalElements.forEach(element => {
        element.textContent = siteSettings.subtotal_label || 'المجموع الفرعي:';
    });

    const deliveryFeesElements = document.querySelectorAll('.delivery-fees-label, [data-setting="delivery_fees_label"]');
    deliveryFeesElements.forEach(element => {
        element.textContent = siteSettings.delivery_fees_label || 'رسوم التوصيل:';
    });

    const totalAmountElements = document.querySelectorAll('.total-amount-label, [data-setting="total_amount_label"]');
    totalAmountElements.forEach(element => {
        element.textContent = siteSettings.total_amount_label || 'المجموع الكلي:';
    });

    const customerInfoElements = document.querySelectorAll('.customer-info-label, [data-setting="customer_info_label"]');
    customerInfoElements.forEach(element => {
        element.textContent = siteSettings.customer_info_label || 'معلومات العميل';
    });

    const fullNameElements = document.querySelectorAll('.full-name-label, [data-setting="full_name_label"]');
    fullNameElements.forEach(element => {
        element.textContent = siteSettings.full_name_label || 'الاسم الكامل';
    });

    const phoneNumberElements = document.querySelectorAll('.phone-number-label, [data-setting="phone_number_label"]');
    phoneNumberElements.forEach(element => {
        element.textContent = siteSettings.phone_number_label || 'رقم الهاتف';
    });

    const fullAddressElements = document.querySelectorAll('.full-address-label, [data-setting="full_address_label"]');
    fullAddressElements.forEach(element => {
        element.textContent = siteSettings.full_address_label || 'العنوان الكامل';
    });

    const deliveryAreaElements = document.querySelectorAll('.delivery-area-label, [data-setting="delivery_area_label"]');
    deliveryAreaElements.forEach(element => {
        element.textContent = siteSettings.delivery_area_label || 'منطقة التوصيل';
    });

    const chooseDeliveryElements = document.querySelectorAll('.choose-delivery-label, [data-setting="choose_delivery_label"]');
    chooseDeliveryElements.forEach(element => {
        element.textContent = siteSettings.choose_delivery_label || 'اختر منطقة التوصيل';
    });

    const additionalNotesElements = document.querySelectorAll('.additional-notes-label, [data-setting="additional_notes_label"]');
    additionalNotesElements.forEach(element => {
        element.textContent = siteSettings.additional_notes_label || 'ملاحظات إضافية';
    });

    // Update homepage-specific settings
    console.log('🔄 Applying homepage-specific settings...');

    // Hero section settings
    const heroTitleLine1Elements = document.querySelectorAll('[data-setting="hero_title_line1"]');
    heroTitleLine1Elements.forEach(element => {
        element.textContent = siteSettings.hero_title_line1 || 'اكتشف عالم الجمال الحقيقي';
    });

    const heroTitleLine2Elements = document.querySelectorAll('[data-setting="hero_title_line2"]');
    heroTitleLine2Elements.forEach(element => {
        element.textContent = siteSettings.hero_title_line2 || 'مع متجر Care';
    });

    const heroSubtitleElements = document.querySelectorAll('[data-setting="hero_subtitle"]');
    heroSubtitleElements.forEach(element => {
        element.textContent = siteSettings.hero_subtitle || 'منتجات عناية فاخرة من أفضل العلامات التجارية العالمية - جودة استثنائية وخدمة لا مثيل لها في جميع أنحاء العراق';
    });

    // Trust indicators
    for (let i = 1; i <= 3; i++) {
        const trustElements = document.querySelectorAll(`[data-setting="trust_indicator_${i}"]`);
        trustElements.forEach(element => {
            element.textContent = siteSettings[`trust_indicator_${i}`] || '';
        });

        // Trust indicator icons
        const trustIconElements = document.querySelectorAll(`[data-setting="trust_indicator_${i}_icon"]`);
        trustIconElements.forEach(element => {
            const iconClass = siteSettings[`trust_indicator_${i}_icon`] || 'fas fa-certificate';
            element.className = iconClass;
        });
    }

    // Trust indicators visibility control
    const trustIndicatorsSection = document.querySelector('.trust-indicators');
    if (trustIndicatorsSection) {
        const trustIndicatorsEnabled = siteSettings.trust_indicators_enabled === '1' || siteSettings.trust_indicators_enabled === true || siteSettings.trust_indicators_enabled === undefined;
        trustIndicatorsSection.style.display = trustIndicatorsEnabled ? 'flex' : 'none';
        console.log(`🔒 Trust indicators ${trustIndicatorsEnabled ? 'enabled' : 'disabled'} (setting value: ${siteSettings.trust_indicators_enabled})`);
    }

    // Featured product in banner visibility control
    const featuredProductSection = document.querySelector('.hero-showcase');
    if (featuredProductSection) {
        const featuredProductEnabled = siteSettings.featured_product_enabled === '1' || siteSettings.featured_product_enabled === true || siteSettings.featured_product_enabled === undefined;
        featuredProductSection.style.display = featuredProductEnabled ? 'block' : 'none';
        console.log(`⭐ Featured product in banner ${featuredProductEnabled ? 'enabled' : 'disabled'} (setting value: ${siteSettings.featured_product_enabled})`);
    }

    // Hero background image
    const heroBackgroundElements = document.querySelectorAll('.hero-background');
    heroBackgroundElements.forEach(element => {
        if (siteSettings.hero_background_enabled === '1' && siteSettings.hero_background_url) {
            // Use custom background image
            element.style.background = `linear-gradient(135deg, rgba(74, 144, 164, 0.9), rgba(44, 62, 80, 0.8)), url('${siteSettings.hero_background_url}') center/cover`;
        } else {
            // Use default background
            element.style.background = `linear-gradient(135deg, rgba(74, 144, 164, 0.9), rgba(44, 62, 80, 0.8)), url('https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=1920&h=1080&fit=crop') center/cover`;
        }
    });

    // Banner background image (main banner/hero section)
    const bannerBackgroundElements = document.querySelectorAll('.hero-section, .banner-section');
    bannerBackgroundElements.forEach(element => {
        if (siteSettings.banner_background_enabled === '1' && siteSettings.banner_background_url) {
            // Use custom banner background image
            element.style.background = `linear-gradient(135deg, rgba(74, 144, 164, 0.9), rgba(44, 62, 80, 0.8)), url('${siteSettings.banner_background_url}') center/cover`;
        } else {
            // Keep default background (no change needed as it's already set in CSS)
        }
    });

    // Hero showcase image
    const heroShowcaseImages = document.querySelectorAll('.showcase-image');
    heroShowcaseImages.forEach(element => {
        if (siteSettings.hero_showcase_enabled === '1' && siteSettings.hero_showcase_url) {
            // Use custom showcase image
            element.src = siteSettings.hero_showcase_url;
        } else {
            // Use default showcase image
            element.src = 'https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=300&h=300&fit=crop';
        }
    });

    // Testimonials section
    const testimonialsSection = document.getElementById('testimonials-section');
    if (testimonialsSection) {
        if (siteSettings.testimonials_enabled === '1') {
            testimonialsSection.style.display = 'block';

            // Update testimonials content
            const testimonialsBadgeElements = document.querySelectorAll('[data-setting="testimonials_badge"]');
            testimonialsBadgeElements.forEach(element => {
                element.textContent = siteSettings.testimonials_badge || 'آراء عملائنا';
            });

            const testimonialsTitleElements = document.querySelectorAll('[data-setting="testimonials_title"]');
            testimonialsTitleElements.forEach(element => {
                element.textContent = siteSettings.testimonials_title || 'ماذا يقول عملاؤنا عنا؟';
            });

            const testimonialsSubtitleElements = document.querySelectorAll('[data-setting="testimonials_subtitle"]');
            testimonialsSubtitleElements.forEach(element => {
                element.textContent = siteSettings.testimonials_subtitle || 'تجارب حقيقية من عملائنا الكرام الذين جربوا منتجاتنا وخدماتنا المتميزة';
            });

            // Testimonial content is handled by the main loop above (lines 916-983)

            // Update testimonials statistics
            const totalReviewsElements = document.querySelectorAll('[data-setting="testimonials_total_reviews"]');
            totalReviewsElements.forEach(element => {
                element.textContent = siteSettings.testimonials_total_reviews || '2,500+';
            });

            const averageRatingElements = document.querySelectorAll('[data-setting="testimonials_average_rating"]');
            averageRatingElements.forEach(element => {
                element.textContent = siteSettings.testimonials_average_rating || '4.9';
            });

            const satisfactionElements = document.querySelectorAll('[data-setting="testimonials_satisfaction"]');
            satisfactionElements.forEach(element => {
                element.textContent = siteSettings.testimonials_satisfaction || '98%';
            });

            // Handle testimonials stats visibility
            const testimonialsStatsElements = document.querySelectorAll('.testimonials-stats, .testimonial-stat, [data-setting="testimonials_total_reviews"], [data-setting="testimonials_average_rating"], [data-setting="testimonials_satisfaction"]');
            testimonialsStatsElements.forEach(element => {
                const statsEnabled = siteSettings.testimonials_stats_enabled === '1' || siteSettings.testimonials_stats_enabled === true;
                const parentElement = element.closest('.testimonials-stats, .testimonial-stat, .stats-container, .stats-grid');
                if (parentElement) {
                    parentElement.style.display = statsEnabled ? 'block' : 'none';
                } else {
                    element.style.display = statsEnabled ? 'block' : 'none';
                }
            });
        } else {
            testimonialsSection.style.display = 'none';
        }
    }

    // Newsletter section
    const newsletterSection = document.getElementById('newsletter-section');
    if (newsletterSection) {
        if (siteSettings.newsletter_enabled === '1') {
            newsletterSection.style.display = 'block';

            // Update newsletter content
            const newsletterTitleElements = document.querySelectorAll('[data-setting="newsletter_title"]');
            newsletterTitleElements.forEach(element => {
                element.textContent = siteSettings.newsletter_title || 'اشترك في نشرتنا الإخبارية';
            });

            const newsletterSubtitleElements = document.querySelectorAll('[data-setting="newsletter_subtitle"]');
            newsletterSubtitleElements.forEach(element => {
                element.textContent = siteSettings.newsletter_subtitle || 'احصل على أحدث العروض والنصائح للعناية بالبشرة والشعر مباشرة في بريدك الإلكتروني';
            });

            // Update newsletter benefits
            for (let i = 1; i <= 3; i++) {
                const benefitElements = document.querySelectorAll(`[data-setting="newsletter_benefit_${i}"]`);
                benefitElements.forEach(element => {
                    element.textContent = siteSettings[`newsletter_benefit_${i}`] || '';
                });
            }

            const privacyTextElements = document.querySelectorAll('[data-setting="newsletter_privacy_text"]');
            privacyTextElements.forEach(element => {
                element.textContent = siteSettings.newsletter_privacy_text || 'نحن نحترم خصوصيتك ولن نشارك بريدك الإلكتروني مع أي طرف ثالث';
            });

            // Update newsletter statistics
            const subscribersCountElements = document.querySelectorAll('[data-setting="newsletter_subscribers_count"]');
            subscribersCountElements.forEach(element => {
                element.textContent = siteSettings.newsletter_subscribers_count || '5,000+';
            });

            const satisfactionRateElements = document.querySelectorAll('[data-setting="newsletter_satisfaction_rate"]');
            satisfactionRateElements.forEach(element => {
                element.textContent = siteSettings.newsletter_satisfaction_rate || '95%';
            });

            const weeklyTipsElements = document.querySelectorAll('[data-setting="newsletter_weekly_tips"]');
            weeklyTipsElements.forEach(element => {
                element.textContent = siteSettings.newsletter_weekly_tips || '3';
            });
        } else {
            newsletterSection.style.display = 'none';
        }
    }

    // Categories section
    const categoriesSection = document.getElementById('categories-section');
    if (categoriesSection) {
        if (siteSettings.categories_enabled === '1') {
            categoriesSection.style.display = 'block';

            // Update categories content
            const categoriesBadgeElements = document.querySelectorAll('[data-setting="categories_badge"]');
            categoriesBadgeElements.forEach(element => {
                element.textContent = siteSettings.categories_badge || 'تسوق حسب الفئة';
            });

            const categoriesTitleElements = document.querySelectorAll('[data-setting="categories_title"]');
            categoriesTitleElements.forEach(element => {
                element.textContent = siteSettings.categories_title || 'اكتشف مجموعاتنا المتنوعة';
            });

            const categoriesSubtitleElements = document.querySelectorAll('[data-setting="categories_subtitle"]');
            categoriesSubtitleElements.forEach(element => {
                element.textContent = siteSettings.categories_subtitle || 'تصفح مجموعة واسعة من منتجات العناية بالبشرة والشعر المصنفة بعناية لتناسب احتياجاتك';
            });

            // Update categories
            for (let i = 1; i <= 4; i++) {
                const nameElements = document.querySelectorAll(`[data-setting="category_${i}_name"]`);
                nameElements.forEach(element => {
                    element.textContent = siteSettings[`category_${i}_name`] || '';
                });

                const descriptionElements = document.querySelectorAll(`[data-setting="category_${i}_description"]`);
                descriptionElements.forEach(element => {
                    element.textContent = siteSettings[`category_${i}_description`] || '';
                });

                const countElements = document.querySelectorAll(`[data-setting="category_${i}_count"]`);
                countElements.forEach(element => {
                    element.textContent = siteSettings[`category_${i}_count`] || '';
                });
            }
        } else {
            categoriesSection.style.display = 'none';
        }
    }

    // Featured product in hero
    const featuredProductNameElements = document.querySelectorAll('[data-setting="featured_product_name"]');
    featuredProductNameElements.forEach(element => {
        element.textContent = siteSettings.featured_product_name || 'سيروم فيتامين سي المتقدم';
    });

    const featuredProductPriceElements = document.querySelectorAll('[data-setting="featured_product_price"]');
    featuredProductPriceElements.forEach(element => {
        element.textContent = siteSettings.featured_product_price || '45,000 د.ع';
    });

    const featuredProductOriginalPriceElements = document.querySelectorAll('[data-setting="featured_product_original_price"]');
    featuredProductOriginalPriceElements.forEach(element => {
        element.textContent = siteSettings.featured_product_original_price || '60,000 د.ع';
    });

    // Featured products section
    const featuredProductsBadgeElements = document.querySelectorAll('[data-setting="featured_products_badge"]');
    featuredProductsBadgeElements.forEach(element => {
        element.textContent = siteSettings.featured_products_badge || 'منتجات مختارة بعناية';
    });

    const featuredProductsTitleElements = document.querySelectorAll('[data-setting="featured_products_title"]');
    featuredProductsTitleElements.forEach(element => {
        element.textContent = siteSettings.featured_products_title || 'المنتجات المميزة';
    });

    const featuredProductsSubtitleElements = document.querySelectorAll('[data-setting="featured_products_subtitle"]');
    featuredProductsSubtitleElements.forEach(element => {
        element.textContent = siteSettings.featured_products_subtitle || 'اكتشف مجموعتنا المختارة بعناية من أفضل منتجات العناية بالبشرة والشعر من العلامات التجارية الموثوقة';
    });

    // Features section
    const featuresBadgeElements = document.querySelectorAll('[data-setting="features_badge"]');
    featuresBadgeElements.forEach(element => {
        element.textContent = siteSettings.features_badge || 'خدماتنا المميزة';
    });

    const featuresTitleElements = document.querySelectorAll('[data-setting="features_title"]');
    featuresTitleElements.forEach(element => {
        element.textContent = siteSettings.features_title || 'لماذا تختار متجر Care؟';
    });

    const featuresSubtitleElements = document.querySelectorAll('[data-setting="features_subtitle"]');
    featuresSubtitleElements.forEach(element => {
        element.textContent = siteSettings.features_subtitle || 'نقدم لك تجربة تسوق استثنائية مع خدمات متطورة وضمانات شاملة تضمن رضاك التام';
    });

    // Individual features (1-6)
    for (let i = 1; i <= 6; i++) {
        const featureTitleElements = document.querySelectorAll(`[data-setting="feature_${i}_title"]`);
        featureTitleElements.forEach(element => {
            element.textContent = siteSettings[`feature_${i}_title`] || '';
        });

        const featureDescriptionElements = document.querySelectorAll(`[data-setting="feature_${i}_description"]`);
        featureDescriptionElements.forEach(element => {
            element.textContent = siteSettings[`feature_${i}_description`] || '';
        });
    }

    // Benefits section (1-6)
    console.log('🔄 Applying benefits section settings...');
    for (let i = 1; i <= 6; i++) {
        const benefitTitleElements = document.querySelectorAll(`[data-setting="benefit_${i}_title"]`);
        benefitTitleElements.forEach(element => {
            element.textContent = siteSettings[`benefit_${i}_title`] || '';
        });

        const benefitDescriptionElements = document.querySelectorAll(`[data-setting="benefit_${i}_description"]`);
        benefitDescriptionElements.forEach(element => {
            element.textContent = siteSettings[`benefit_${i}_description`] || '';
        });

        const benefitDetailsElements = document.querySelectorAll(`[data-setting="benefit_${i}_details"]`);
        benefitDetailsElements.forEach(element => {
            element.textContent = siteSettings[`benefit_${i}_details`] || '';
        });

        const benefitStatValueElements = document.querySelectorAll(`[data-setting="benefit_${i}_stat_value"]`);
        benefitStatValueElements.forEach(element => {
            element.textContent = siteSettings[`benefit_${i}_stat_value`] || '';
        });

        const benefitStatLabelElements = document.querySelectorAll(`[data-setting="benefit_${i}_stat_label"]`);
        benefitStatLabelElements.forEach(element => {
            element.textContent = siteSettings[`benefit_${i}_stat_label`] || '';
        });

        const benefitIconElements = document.querySelectorAll(`[data-setting="benefit_${i}_icon"]`);
        benefitIconElements.forEach(element => {
            if (element.tagName === 'I') {
                // Update icon class for <i> elements
                const iconClass = siteSettings[`benefit_${i}_icon`] || 'fas fa-star';
                element.className = iconClass;
            }
        });

        // Handle visibility for Benefits 4, 5, and 6 based on enabled status
        if (i >= 4) {
            const benefitEnabled = siteSettings[`benefit_${i}_enabled`] === '1' || siteSettings[`benefit_${i}_enabled`] === true;
            const benefitCards = document.querySelectorAll(`.benefit-card:nth-child(${i}), .benefit-item:nth-child(${i}), [data-benefit="${i}"]`);
            benefitCards.forEach(card => {
                card.style.display = benefitEnabled ? '' : 'none';
            });
            console.log(`💎 Benefit ${i} ${benefitEnabled ? 'enabled' : 'disabled'} (setting value: ${siteSettings[`benefit_${i}_enabled`]})`);
        }
    }

    // Timeline stats section
    console.log('🔄 Applying timeline stats settings...');
    const timelineStatYearsElements = document.querySelectorAll('[data-setting="timeline_stat_years"]');
    timelineStatYearsElements.forEach(element => {
        element.textContent = siteSettings.timeline_stat_years || '6+';
    });

    const timelineStatCustomersElements = document.querySelectorAll('[data-setting="timeline_stat_customers"]');
    timelineStatCustomersElements.forEach(element => {
        element.textContent = siteSettings.timeline_stat_customers || '5000+';
    });

    const timelineStatProductsElements = document.querySelectorAll('[data-setting="timeline_stat_products"]');
    timelineStatProductsElements.forEach(element => {
        element.textContent = siteSettings.timeline_stat_products || '500+';
    });

    const timelineStatBrandsElements = document.querySelectorAll('[data-setting="timeline_stat_brands"]');
    timelineStatBrandsElements.forEach(element => {
        element.textContent = siteSettings.timeline_stat_brands || '50+';
    });

    // Benefits summary section
    console.log('🔄 Applying benefits summary settings...');
    const benefitsSummaryTitleElements = document.querySelectorAll('[data-setting="benefits_summary_title"]');
    benefitsSummaryTitleElements.forEach(element => {
        element.textContent = siteSettings.benefits_summary_title || 'لماذا تختار منتجاتنا؟';
    });

    const benefitsSummaryDescriptionElements = document.querySelectorAll('[data-setting="benefits_summary_description"]');
    benefitsSummaryDescriptionElements.forEach(element => {
        element.textContent = siteSettings.benefits_summary_description || 'نحن نؤمن بأن الجمال الحقيقي يأتي من منتجات عالية الجودة وآمنة. لذلك نقدم لك مجموعة منتجات مختارة بعناية لتحقيق أفضل النتائج.';
    });

    // Benefits summary stats (1-3)
    for (let i = 1; i <= 3; i++) {
        const statValueElements = document.querySelectorAll(`[data-setting="benefits_summary_stat${i}_value"]`);
        statValueElements.forEach(element => {
            element.textContent = siteSettings[`benefits_summary_stat${i}_value`] || '';
        });

        const statLabelElements = document.querySelectorAll(`[data-setting="benefits_summary_stat${i}_label"]`);
        statLabelElements.forEach(element => {
            element.textContent = siteSettings[`benefits_summary_stat${i}_label`] || '';
        });
    }

    // About section
    const aboutBadgeElements = document.querySelectorAll('[data-setting="about_badge"]');
    aboutBadgeElements.forEach(element => {
        element.textContent = siteSettings.about_badge || 'قصتنا';
    });

    const aboutTitleElements2 = document.querySelectorAll('[data-setting="about_title"]');
    aboutTitleElements2.forEach(element => {
        element.textContent = siteSettings.about_title || 'من نحن';
    });

    const aboutSubtitleElements = document.querySelectorAll('[data-setting="about_subtitle"]');
    aboutSubtitleElements.forEach(element => {
        element.textContent = siteSettings.about_subtitle || 'رحلتنا في عالم الجمال والعناية بدأت من شغف حقيقي بتقديم الأفضل لعملائنا الكرام في جميع أنحاء العراق';
    });

    const aboutDescription1Elements = document.querySelectorAll('[data-setting="about_description_1"]');
    aboutDescription1Elements.forEach(element => {
        element.textContent = siteSettings.about_description_1 || 'منذ تأسيسنا عام 2018، نسعى لتقديم أفضل منتجات العناية بالبشرة والشعر من أرقى العلامات التجارية العالمية. نؤمن بأن الجمال الحقيقي يبدأ من العناية الصحيحة والمنتجات عالية الجودة.';
    });

    const aboutDescription2Elements = document.querySelectorAll('[data-setting="about_description_2"]');
    aboutDescription2Elements.forEach(element => {
        element.textContent = siteSettings.about_description_2 || 'فريقنا المتخصص يعمل بجد لاختيار كل منتج بعناية فائقة، مع التأكد من أصالته وجودته العالية. نحن لسنا مجرد متجر، بل شريكك الموثوق في رحلة العناية والجمال.';
    });

    // Statistics
    const statCustomersElements = document.querySelectorAll('[data-setting="stat_customers"]');
    statCustomersElements.forEach(element => {
        element.textContent = siteSettings.stat_customers || '12,500+';
    });

    const statOrdersElements = document.querySelectorAll('[data-setting="stat_orders"]');
    statOrdersElements.forEach(element => {
        element.textContent = siteSettings.stat_orders || '25,000+';
    });

    const statYearsElements = document.querySelectorAll('[data-setting="stat_years"]');
    statYearsElements.forEach(element => {
        element.textContent = siteSettings.stat_years || '7+';
    });

    const statRatingElements = document.querySelectorAll('[data-setting="stat_rating"]');
    statRatingElements.forEach(element => {
        element.textContent = siteSettings.stat_rating || '4.9';
    });

    // Apply page header background images
    console.log('🔄 Applying page header background images...');
    applyPageHeaderBackgrounds();

    // Generic data-setting handler - Apply all settings to elements with data-setting attributes
    console.log('🔄 Applying generic data-setting attributes...');
    let updatedElementsCount = 0;
    Object.keys(siteSettings).forEach(settingKey => {
        const elements = document.querySelectorAll(`[data-setting="${settingKey}"]`);
        if (elements.length > 0) {
            console.log(`🔄 Updating ${elements.length} elements with data-setting="${settingKey}" to: "${String(siteSettings[settingKey]).substring(0, 50)}${String(siteSettings[settingKey]).length > 50 ? '...' : ''}"`);
            elements.forEach(element => {
                try {
                    // Handle different element types appropriately
                    if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
                        element.value = siteSettings[settingKey] || '';
                    } else if (element.tagName === 'IMG') {
                        element.src = siteSettings[settingKey] || '';
                        element.alt = element.alt || settingKey;
                    } else if (element.tagName === 'A' && settingKey.includes('url')) {
                        element.href = siteSettings[settingKey] || '#';
                        if (element.textContent.trim() === '') {
                            element.textContent = siteSettings[settingKey] || '';
                        }
                    } else if (element.tagName === 'I' && settingKey.includes('icon')) {
                        // For icon elements, update the class instead of text content
                        element.className = siteSettings[settingKey] || 'fas fa-star';
                    } else if (element.tagName === 'I' || element.classList.contains('fas') || element.classList.contains('far') || element.classList.contains('fab') || element.classList.contains('fal')) {
                        // Skip icon elements to prevent overriding their classes with text
                        console.log(`🎯 Skipping icon element with data-setting="${settingKey}" to preserve icon display`);
                    } else {
                        // For most elements, update text content
                        element.textContent = siteSettings[settingKey] || '';
                    }
                    updatedElementsCount++;
                } catch (error) {
                    console.error(`❌ Error updating element with data-setting="${settingKey}":`, error);
                }
            });
        }
    });
    console.log(`✅ Updated ${updatedElementsCount} elements with data-setting attributes`);

    // Update page title if on homepage
    if (document.title.includes('Care') && siteSettings.business_name !== 'Care') {
        document.title = document.title.replace('Care', siteSettings.business_name);
    }

    // Handle timeline section visibility
    const timelineSection = document.getElementById('timeline-section');
    if (timelineSection) {
        const timelineEnabled = siteSettings.timeline_enabled === '1' || siteSettings.timeline_enabled === true;
        timelineSection.style.display = timelineEnabled ? 'block' : 'none';
        console.log(`🕒 Timeline section ${timelineEnabled ? 'enabled' : 'disabled'} (setting value: ${siteSettings.timeline_enabled})`);

        if (timelineEnabled) {
            // Handle individual timeline items visibility
            for (let i = 1; i <= 6; i++) {
                const timelineItems = document.querySelectorAll(`.timeline-item:nth-child(${i + 1})`); // +1 because timeline-line is first child
                timelineItems.forEach(item => {
                    const itemEnabled = siteSettings[`timeline_${i}_enabled`] === '1' || siteSettings[`timeline_${i}_enabled`] === true;
                    item.style.display = itemEnabled ? 'block' : 'none';
                });
            }

            // Handle timeline stats visibility
            const timelineStatsElements = document.querySelectorAll('.timeline-stats, .timeline-stat');
            timelineStatsElements.forEach(element => {
                const statsEnabled = siteSettings.timeline_stats_enabled === '1' || siteSettings.timeline_stats_enabled === true;
                element.style.display = statsEnabled ? 'block' : 'none';
            });
        }

        // Debug timeline stats values
        console.log('🔍 Timeline stats values:', {
            years: siteSettings.timeline_stat_years,
            customers: siteSettings.timeline_stat_customers,
            products: siteSettings.timeline_stat_products,
            brands: siteSettings.timeline_stat_brands
        });
    }

    // Handle benefits showcase section visibility
    const benefitsShowcaseSection = document.getElementById('benefits-showcase-section');
    if (benefitsShowcaseSection) {
        const benefitsShowcaseEnabled = siteSettings.benefits_showcase_enabled === '1' || siteSettings.benefits_showcase_enabled === true;
        benefitsShowcaseSection.style.display = benefitsShowcaseEnabled ? 'block' : 'none';
        console.log(`⭐ Benefits showcase section ${benefitsShowcaseEnabled ? 'enabled' : 'disabled'} (setting value: ${siteSettings.benefits_showcase_enabled})`);

        // Debug benefits values
        console.log('🔍 Benefits values:', {
            benefit_1_title: siteSettings.benefit_1_title,
            benefit_1_description: siteSettings.benefit_1_description,
            benefit_2_title: siteSettings.benefit_2_title,
            benefit_2_description: siteSettings.benefit_2_description
        });
    }

    console.log('✅ Site settings applied successfully');

    // Force a repaint to ensure all changes are visible
    document.body.style.display = 'none';
    document.body.offsetHeight; // Trigger reflow
    document.body.style.display = '';

    // Trigger custom event to notify other scripts that settings have been loaded
    window.dispatchEvent(new CustomEvent('siteSettingsLoaded', { detail: siteSettings }));
}

// Get a specific setting value
function getSetting(key, defaultValue = '') {
    return siteSettings[key] || defaultValue;
}

// Get delivery fee based on location
function getDeliveryFee(location) {
    switch(location) {
        case 'baghdad':
            return parseInt(siteSettings.delivery_baghdad || '5000');
        case 'other':
            return parseInt(siteSettings.delivery_other || '10000');
        default:
            return 0;
    }
}

// Get free delivery threshold
function getFreeDeliveryThreshold() {
    return parseInt(siteSettings.free_delivery_threshold || '50000');
}

// Check if order qualifies for free delivery
function qualifiesForFreeDelivery(subtotal) {
    const threshold = getFreeDeliveryThreshold();
    return subtotal >= threshold;
}

// Get formatted delivery fee text for display
function getDeliveryFeeText(location) {
    const fee = getDeliveryFee(location);
    if (fee === 0) return 'مجاني';

    return new Intl.NumberFormat('ar-IQ', {
        style: 'currency',
        currency: 'IQD',
        minimumFractionDigits: 0
    }).format(fee).replace('IQD', 'د.ع');
}

// Cleanup subscription on page unload
function cleanupSubscription() {
    if (settingsSubscription) {
        settingsSubscription.unsubscribe();
        settingsSubscription = null;
    }
}

// Listen for storage events for cross-tab communication
function setupCrossTabCommunication() {
    window.addEventListener('storage', function(e) {
        if (e.key === 'siteSettingsLastUpdate') {
            console.log('🔄 Settings updated from another tab, reloading...');
            // Reload settings when updated from another tab
            loadSiteSettings();
        }
    });

    // Listen for custom settings update events
    window.addEventListener('siteSettingsUpdated', function(e) {
        console.log('🔄 Settings updated via custom event, reloading...');
        loadSiteSettings();
    });

    // Listen for real-time updates from admin panel
    window.addEventListener('adminSettingsChanged', function(e) {
        console.log('🔄 Admin settings changed, reloading...');
        loadSiteSettings();
    });

    // Listen for postMessage from admin panel (if in iframe or popup)
    window.addEventListener('message', function(e) {
        if (e.data && e.data.type === 'siteSettingsUpdated') {
            console.log('🔄 Settings updated via postMessage, reloading...');
            loadSiteSettings();
        }
    });

    // Listen for BroadcastChannel messages
    try {
        const bc = new BroadcastChannel('site-settings-updates');
        bc.addEventListener('message', function(e) {
            if (e.data && e.data.type === 'settingsUpdated') {
                console.log('🔄 Settings updated via BroadcastChannel, reloading...');
                loadSiteSettings();
            }
        });
    } catch (e) {
        console.log('BroadcastChannel not supported, using localStorage fallback');
    }

    // Set up periodic check for settings updates (every 30 seconds)
    setInterval(function() {
        const lastUpdate = localStorage.getItem('siteSettingsLastUpdate');
        if (lastUpdate) {
            const lastUpdateTime = parseInt(lastUpdate);
            const now = Date.now();
            // If settings were updated in the last 30 seconds, reload
            if (now - lastUpdateTime < 30000) {
                console.log('🔄 Periodic check detected recent settings update, reloading...');
                loadSiteSettings();
                // Clear the flag to avoid repeated reloads
                localStorage.removeItem('siteSettingsLastUpdate');
            }
        }
    }, 5000); // Check every 5 seconds
}

// Initialize settings when DOM is loaded
function initializeSiteSettings(retryCount = 0) {
    const maxRetries = 50; // 5 seconds total (50 * 100ms)

    // Check if Supabase is available
    if (typeof window !== 'undefined' && window.supabase && typeof window.supabase.createClient === 'function') {
        console.log('✅ Supabase available, loading site settings...');

        // Check for pending updates before loading
        const lastUpdate = localStorage.getItem('siteSettingsLastUpdate');
        if (lastUpdate) {
            const lastUpdateTime = parseInt(lastUpdate);
            const now = Date.now();
            // If settings were updated in the last 2 minutes, force reload
            if (now - lastUpdateTime < 120000) {
                console.log('🔄 Detected recent settings update on page load, forcing reload...');
                localStorage.removeItem('siteSettingsLastUpdate');
            }
        }

        loadSiteSettings();
        setupCrossTabCommunication();
    } else if (retryCount < maxRetries) {
        console.log(`⏳ Waiting for Supabase to load... (attempt ${retryCount + 1}/${maxRetries})`);
        // Retry after a short delay
        setTimeout(() => initializeSiteSettings(retryCount + 1), 100);
    } else {
        console.warn('⚠️ Supabase failed to load after maximum retries, using default settings');
        setDefaultSettings();
        applySiteSettings();

        // Mark as loaded with defaults
        if (window.siteSettingsManager) {
            window.siteSettingsManager.loaded = true;
            window.siteSettingsManager.loading = false;
        }

        // Dispatch event with defaults
        window.dispatchEvent(new CustomEvent('siteSettingsLoaded', {
            detail: { settings: siteSettings, usingDefaults: true, error: 'Supabase not available' }
        }));
    }
}

// Start initialization when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeSiteSettings();
});

// Also try to initialize immediately if DOM is already loaded
if (document.readyState === 'loading') {
    // DOM is still loading, wait for DOMContentLoaded
} else {
    // DOM is already loaded
    setTimeout(initializeSiteSettings, 50);
}

// Add a fallback mechanism to ensure settings are applied
// This helps with timing issues on different pages
setTimeout(() => {
    if (window.siteSettingsManager && window.siteSettingsManager.loaded) {
        console.log('🔄 Fallback: Re-applying site settings to ensure consistency');
        applySiteSettings();
    }
}, 1000);

// Cleanup on page unload
window.addEventListener('beforeunload', cleanupSubscription);

// Manual refresh function for debugging
function manualRefresh() {
    console.log('🔄 Manual refresh triggered');
    return loadSiteSettings();
}

// Assign functions to the already created siteSettingsManager object
Object.assign(window.siteSettingsManager, {
    loadSiteSettings,
    loadDeliverySettings,
    applySiteSettings,
    getCurrentSettings: () => siteSettings,
    getSetting,
    getDeliveryFee,
    getFreeDeliveryThreshold,
    qualifiesForFreeDelivery,
    getDeliveryFeeText,
    setupRealTimeSubscription,
    cleanupSubscription,
    manualRefresh
});

console.log('✅ Site settings manager initialized with functions');

// Debug function for troubleshooting
window.debugSiteSettings = function() {
    console.log('🔍 === SITE SETTINGS DEBUG ===');
    console.log('📋 Site Settings Manager:', {
        exists: !!window.siteSettingsManager,
        loaded: window.siteSettingsManager?.loaded,
        loading: window.siteSettingsManager?.loading,
        functions: window.siteSettingsManager ? Object.keys(window.siteSettingsManager) : 'N/A'
    });

    console.log('🌐 Supabase Status:', {
        library: !!window.supabase,
        createClient: !!(window.supabase?.createClient),
        globalClient: !!window.globalSupabaseClient
    });

// Debug function to test delivery settings loading specifically
window.debugDeliverySettings = async function() {
    console.log('🚚 === DELIVERY SETTINGS DEBUG ===');
    try {
        console.log('🔄 Testing delivery settings loading...');
        await loadDeliverySettings();
        console.log('✅ Delivery settings test completed');
        console.log('📊 Current delivery settings:', {
            delivery_baghdad: siteSettings.delivery_baghdad,
            delivery_other: siteSettings.delivery_other,
            free_delivery_threshold: siteSettings.free_delivery_threshold
        });
    } catch (error) {
        console.error('❌ Delivery settings test failed:', error);
    }
    console.log('🚚 === DEBUG COMPLETE ===');
};

// Function to clear any potential caches that might cause malformed queries
window.clearSupabaseCaches = function() {
    console.log('🧹 Clearing Supabase caches...');

    // Clear localStorage items that might contain cached queries
    const keysToRemove = [];
    for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && (key.includes('supabase') || key.includes('delivery') || key.includes('system_settings'))) {
            keysToRemove.push(key);
        }
    }

    keysToRemove.forEach(key => {
        localStorage.removeItem(key);
        console.log('🗑️ Removed localStorage key:', key);
    });

    // Clear sessionStorage items
    const sessionKeysToRemove = [];
    for (let i = 0; i < sessionStorage.length; i++) {
        const key = sessionStorage.key(i);
        if (key && (key.includes('supabase') || key.includes('delivery') || key.includes('system_settings'))) {
            sessionKeysToRemove.push(key);
        }
    }

    sessionKeysToRemove.forEach(key => {
        sessionStorage.removeItem(key);
        console.log('🗑️ Removed sessionStorage key:', key);
    });

    // Reset global Supabase client
    if (window.globalSupabaseClient) {
        console.log('🔄 Resetting global Supabase client...');
        window.globalSupabaseClient = null;
    }

    // Reset local supabase variable
    supabase = null;

    console.log('✅ Cache clearing completed');
};

    console.log('⚙️ Settings:', {
        count: Object.keys(siteSettings).length,
        delivery_baghdad: siteSettings.delivery_baghdad,
        delivery_other: siteSettings.delivery_other,
        free_delivery_threshold: siteSettings.free_delivery_threshold
    });

    console.log('🔍 === DEBUG COMPLETE ===');
    return {
        siteSettingsManager: !!window.siteSettingsManager,
        loaded: window.siteSettingsManager?.loaded,
        settingsCount: Object.keys(siteSettings).length,
        supabaseAvailable: !!window.supabase
    };
};

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">

    <title>اتصل بنا - Care</title>
    <meta name="description" content="تواصل معنا - صفحة الاتصال لمتجر منتجات العناية بالبشرة والشعر في العراق">
    <meta name="keywords" content="اتصل بنا, تواصل, خدمة العملاء, العراق, منتجات العناية">
    <meta name="author" content="Care">
    <meta name="robots" content="index, follow">
    <meta name="theme-color" content="#4a90a4">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <link href="css/page-header-backgrounds.css" rel="stylesheet">
    <link href="css/standardized-typography.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>

    <style>
        /* CSS Variables - Established Design System */
        :root {
            /* Spacing System */
            --spacing-xs: 0.25rem;    /* 4px */
            --spacing-sm: 0.5rem;     /* 8px */
            --spacing-md: 1rem;       /* 16px */
            --spacing-lg: 1.5rem;     /* 24px */
            --spacing-xl: 2rem;       /* 32px */
            --spacing-xxl: 3rem;      /* 48px */

            /* Color System */
            --primary-color: #4a90a4;
            --primary-dark: #2c3e50;
            --primary-light: #6ba4b8;
            --text-primary: #000000;
            --text-secondary: #666666;
            --bg-primary: #ffffff;
            --bg-secondary: #f8f9fa;
            --border-color: #e9ecef;
            --success-color: #28a745;
            --error-color: #dc3545;

            /* Typography */
            --font-family: 'Cairo', sans-serif;
            --font-size-xs: 0.75rem;
            --font-size-sm: 0.875rem;
            --font-size-base: 1rem;
            --font-size-lg: 1.125rem;
            --font-size-xl: 1.25rem;
            --font-size-2xl: 1.5rem;
            --font-size-3xl: 2rem;
            --font-size-4xl: 2.5rem;
            --font-size-5xl: 3.5rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-family);
            background-color: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
            direction: rtl;
            padding-top: 90px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--spacing-xl);
        }

        /* Standardized Header Design - Clean and Simple */
        header {
            background: #121414;
            color: white;
            padding: 1.5rem 0;
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: var(--font-size-4xl);
            font-weight: 700;
            text-decoration: none;
            color: white;
            letter-spacing: 1px;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 1rem;
            margin: 0;
            padding: 0;
        }

        .nav-menu li a {
            color: white;
            text-decoration: none;
            padding: 0.8rem 1.2rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .nav-menu li a:hover,
        .nav-menu li a.active {
            background: var(--primary-color);
            color: white;
        }



        /* Cart Icon */
        .cart-icon {
            position: relative;
            cursor: pointer;
            padding: 0.8rem;
            border-radius: 50%;
            transition: all 0.3s ease;
            color: white;
        }

        .cart-icon:hover {
            background: rgba(255,255,255,0.1);
        }

        .cart-icon i {
            font-size: var(--font-size-2xl);
        }

        .cart-count {
            position: absolute;
            top: 0;
            right: 0;
            background: #e74c3c;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-xs);
            font-weight: 600;
        }

        /* Page Header styles moved to css/page-header-backgrounds.css for standardization */
        /*
        .page-header {
            background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
            color: white;
            text-align: center;
            padding: 6rem 0 4rem 0;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .page-header .container {
            position: relative;
            z-index: 2;
        }

        .page-header h1 {
            font-size: var(--font-size-5xl);
            margin-bottom: 2rem;
            color: white;
            font-weight: 700;
            position: relative;
            letter-spacing: 1px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .page-header h1::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(90deg, transparent, white, transparent);
            border-radius: 2px;
        }

        .page-header p {
            font-size: var(--font-size-xl);
            color: rgba(255,255,255,0.95);
            max-width: 700px;
            margin: 0 auto;
            line-height: 1.7;
            font-weight: 400;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
        }
        */

        /* Contact Section - Design System Compliant */
        .contact-section {
            padding: 4rem 0;
            background: var(--bg-secondary);
        }

        .contact-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: start;
        }

        /* Contact Info Container */
        .contact-info {
            background: white;
            border-radius: 12px;
            padding: 3rem;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .contact-info:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0,0,0,0.15);
            border-color: var(--primary-color);
        }

        .contact-info h2 {
            color: var(--text-primary);
            margin-bottom: 2rem;
            font-size: var(--font-size-2xl);
            font-weight: 700;
            font-family: var(--font-family);
        }

        /* Info Items */
        .info-item {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: var(--bg-secondary);
            border-radius: 8px;
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
        }



        .info-item:hover {
            background: white;
            border-color: var(--primary-color);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        /* Info Icons */
        .info-icon {
            background: var(--primary-color);
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-xl);
            flex-shrink: 0;
            transition: all 0.3s ease;
        }

        .info-item:hover .info-icon {
            background: var(--primary-dark);
        }





        .map-placeholder {
            text-align: center;
            padding: 2rem;
            background: white;
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .map-btn {
            margin-top: 1.5rem;
            padding: 1rem 2rem;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 8px;
            font-family: var(--font-family);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .map-btn:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }

        .info-content h3 {
            color: var(--primary-color);
            margin-bottom: 0.3rem;
            font-size: var(--font-size-lg);
            font-family: var(--font-family);
        }

        .info-content p {
            color: var(--text-secondary);
            margin: 0;
            font-family: var(--font-family);
        }

        /* Contact Form */
        .contact-form {
            background: white;
            border-radius: 12px;
            padding: 3rem;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .contact-form:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0,0,0,0.15);
            border-color: var(--primary-color);
        }

        .contact-form h2 {
            color: var(--text-primary);
            margin-bottom: 2rem;
            font-size: var(--font-size-2xl);
            font-weight: 700;
            font-family: var(--font-family);
        }

        .form-group {
            margin-bottom: 2rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--text-primary);
            font-size: var(--font-size-base);
            font-family: var(--font-family);
        }

        /* Form Fields */
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 1rem;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-family: var(--font-family);
            font-size: var(--font-size-base);
            transition: all 0.3s ease;
            background: white;
            color: var(--text-primary);
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(74, 144, 164, 0.1);
        }

        .form-group input::placeholder,
        .form-group textarea::placeholder {
            color: #999;
            font-weight: 400;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 150px;
            line-height: 1.6;
        }

        /* Submit Button */
        .submit-btn {
            width: 100%;
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: var(--font-family);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .submit-btn:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }

        .submit-btn:disabled {
            background: var(--text-secondary);
            cursor: not-allowed;
            transform: none;
        }

        /* Business Hours */
        .hours-section {
            background: var(--bg-secondary);
            padding: 4rem 0;
        }

        .hours-container {
            max-width: 600px;
            margin: 0 auto;
            text-align: center;
        }

        .hours-container h2 {
            color: var(--primary-color);
            margin-bottom: 2rem;
            font-size: var(--font-size-2xl);
            font-family: var(--font-family);
        }

        .hours-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }

        .hours-card {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .hours-card:hover {
            border-color: var(--primary-color);
            transform: translateY(-2px);
        }

        .hours-card h3 {
            color: var(--primary-color);
            margin-bottom: 1rem;
            font-family: var(--font-family);
        }

        .hours-card p {
            color: var(--text-primary);
            font-size: var(--font-size-base);
            font-family: var(--font-family);
        }

        /* Social Media */
        .social-section {
            padding: 4rem 0;
            text-align: center;
            background: white;
        }

        .social-section h2 {
            color: var(--primary-color);
            margin-bottom: 2rem;
            font-size: var(--font-size-2xl);
            font-family: var(--font-family);
        }

        .social-links {
            display: flex;
            justify-content: center;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .social-link {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            color: white;
            text-decoration: none;
            font-size: var(--font-size-2xl);
            transition: all 0.3s;
        }

        .social-link:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .whatsapp { background: #25d366; }
        .facebook { background: #1877f2; }
        .instagram { background: #e4405f; }
        .telegram { background: #0088cc; }
        .tiktok { background: #000000; }
        .youtube { background: #ff0000; }
        .twitter { background: #1da1f2; }
        .linkedin { background: #0077b5; }
        .snapchat { background: #fffc00; color: #000 !important; }

        /* Success/Error Messages */
        .message {
            padding: 1rem 1.5rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            display: none;
            font-family: var(--font-family);
            font-weight: 500;
            align-items: center;
            gap: 0.5rem;
            direction: rtl;
            text-align: right;
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .message.show {
            display: flex;
            opacity: 1;
            transform: translateY(0);
        }

        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            border-right: 4px solid #28a745;
        }

        .success::before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
            margin-left: 0.5rem;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            border-right: 4px solid #dc3545;
        }

        .error::before {
            content: "⚠";
            color: #dc3545;
            font-weight: bold;
            margin-left: 0.5rem;
        }

        /* Footer - Matching Homepage Design */
        footer {
            background: #0f1111;
            color: white;
            padding: 4rem 0 2rem;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 3rem;
            margin-bottom: 3rem;
        }

        .footer-section h3 {
            color: #FFFFFF;
            margin-bottom: 2rem;
            font-size: var(--font-size-xl);
            font-weight: 700;
        }

        .footer-section p,
        .footer-section a {
            color: #FFFFFF;
            text-decoration: none;
            line-height: 1.8;
            display: flex;
            align-items: center;
            gap: 0.8rem;
            margin-bottom: 0.8rem;
            transition: all 0.3s ease;
        }

        .footer-section a:hover {
            color: #4a90a4;
        }

        .footer-section i {
            font-size: var(--font-size-lg);
            color: #4a90a4;
            min-width: 20px;
        }

        .social-links {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .social-links a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 45px;
            height: 45px;
            border-radius: 50%;
            transition: all 0.3s ease;
            border: 2px solid rgba(255,255,255,0.3);
        }

        .social-links a:hover {
            border-color: #4a90a4;
            background: rgba(74, 144, 164, 0.2);
        }

        .social-links a i {
            font-size: var(--font-size-xl);
            min-width: auto;
        }

        .footer-bottom {
            text-align: center;
            padding: 2rem 0;
            border-top: 1px solid rgba(255,255,255,0.1);
            color: #FFFFFF;
        }

        .business-name {
            color: #4a90a4;
            font-weight: 700;
        }

        /* Enhanced Responsive Design - Cross-Browser Compatible */

        /* Ultra-wide screens (>1200px) - Prevent content stretching */
        @media (min-width: 1201px) {
            .container {
                max-width: 1200px;
                margin: 0 auto;
                padding: 0 2rem;
            }

            .contact-container {
                max-width: 1000px;
                margin: 0 auto;
            }
        }

        /* Large desktop screens (901px-1200px) */
        @media (max-width: 1200px) and (min-width: 901px) {
            .contact-container {
                max-width: 900px;
                margin: 0 auto;
            }
        }

        /* Tablet responsive design (481px-768px) */
        @media (max-width: 768px) and (min-width: 481px) {
            .container {
                padding: 0 2rem;
            }

            .logo {
                font-size: var(--font-size-4xl);
            }

            .page-header h1 {
                font-size: var(--font-size-4xl);
            }

            .page-header p {
                font-size: var(--font-size-xl);
                padding: 0 1.5rem;
            }

            .contact-container {
                grid-template-columns: 1fr;
                gap: 3rem;
            }

            .contact-form {
                padding: 2.5rem;
            }
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
            *,
            *::before,
            *::after {
                -webkit-animation-duration: 0.01ms !important;
                animation-duration: 0.01ms !important;
                -webkit-animation-iteration-count: 1 !important;
                animation-iteration-count: 1 !important;
                -webkit-transition-duration: 0.01ms !important;
                transition-duration: 0.01ms !important;
            }

            .nav-menu,
            .contact-form,
            .contact-info,
            .info-item {
                -webkit-transition: none !important;
                transition: none !important;
            }
        }



        /* Print styles */
        @media print {
            header,
            .nav-menu,
            .cart-icon,
            .social-section {
                display: none;
            }

            .page-header {
                padding: 2rem 0;
                background: none;
                color: #000000;
            }

            .contact-form,
            .contact-info {
                break-inside: avoid;
                page-break-inside: avoid;
                box-shadow: none;
                border: 1px solid #000000;
            }

            .contact-container {
                grid-template-columns: 1fr;
                gap: 2rem;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <a href="index.html" class="logo business-name" aria-label="الصفحة الرئيسية" data-setting="business_name">Care</a>
                <nav>
                    <ul class="nav-menu">
                        <li><a href="index.html">الرئيسية</a></li>
                        <li><a href="products.html">المنتجات</a></li>
                        <li><a href="offers.html">العروض</a></li>
                        <li><a href="guidelines.html">الإرشادات</a></li>
                        <li><a href="faq.html">الأسئلة الشائعة</a></li>
                        <li><a href="contact.html" class="active">اتصل بنا</a></li>
                    </ul>
                </nav>
                <div class="cart-icon" onclick="window.location.href='cart.html'">
                    <i class="fas fa-shopping-cart"></i>
                    <span class="cart-count" id="cartCount">0</span>
                </div>

            </div>
        </div>
    </header>

    <section class="page-header contact-bg" id="contactPageHeader">
        <div class="page-header-decoration"></div>
        <div class="container">
            <div class="page-header-content">
                <nav class="breadcrumb" aria-label="مسار التنقل">
                    <a href="index.html">الرئيسية</a>
                    <span class="separator">←</span>
                    <span class="current">اتصل بنا</span>
                </nav>
                <h1>اتصل بنا</h1>
                <p class="contact-page-description" data-setting="contact_page_description">نحن هنا لمساعدتك والإجابة على جميع استفساراتك</p>
            </div>
        </div>
    </section>

    <main class="contact-section">
        <div class="container">
            <div class="contact-container">
                <div class="contact-info">
                    <h2>معلومات التواصل</h2>
                    
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div class="info-content">
                            <h3>العنوان</h3>
                            <p><span class="business-address"></span><br>بغداد، العراق</p>
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <div class="info-content">
                            <h3>رقم الهاتف</h3>
                            <p class="business-phone">+9647878436643</p>
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="info-content">
                            <h3>البريد الإلكتروني</h3>
                            <p class="business-email"></p>
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fab fa-whatsapp"></i>
                        </div>
                        <div class="info-content">
                            <h3>واتساب</h3>
                            <p class="business-phone">+9647878436643</p>
                        </div>
                    </div>




                </div>

                <div class="contact-form">
                    <h2>أرسل لنا رسالة</h2>
                    
                    <div class="message success" id="successMessage">
                        تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.
                    </div>
                    
                    <div class="message error" id="errorMessage">
                        حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى.
                    </div>

                    <form id="contactForm">
                        <div class="form-group">
                            <label for="name">الاسم الكامل *</label>
                            <input type="text" id="name" name="name" required>
                        </div>

                        <div class="form-group">
                            <label for="email">البريد الإلكتروني</label>
                            <input type="email" id="email" name="email">
                        </div>

                        <div class="form-group">
                            <label for="phone">رقم الهاتف *</label>
                            <input type="tel" id="phone" name="phone" placeholder="07XXXXXXXX" required>
                        </div>

                        <div class="form-group">
                            <label for="subject">الموضوع *</label>
                            <select id="subject" name="subject" required>
                                <option value="">اختر الموضوع</option>
                                <option value="product_inquiry">استفسار عن منتج</option>
                                <option value="order_status">حالة الطلب</option>
                                <option value="complaint">شكوى</option>
                                <option value="suggestion">اقتراح</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="message">الرسالة *</label>
                            <textarea id="message" name="message" placeholder="اكتب رسالتك هنا..." required></textarea>
                        </div>

                        <button type="submit" class="submit-btn" id="submitBtn">
                            <i class="fas fa-paper-plane"></i>
                            إرسال الرسالة
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </main>

    <section class="hours-section">
        <div class="container">
            <div class="hours-container">
                <h2>أوقات العمل</h2>
                <div class="hours-grid">
                    <div class="hours-card">
                        <h3>أيام العمل</h3>
                        <p><span class="working-days">السبت - الخميس</span><br><span class="working-hours">10:00 صباحاً - 5:00 مساءً</span></p>
                    </div>
                    <div class="hours-card">
                        <h3>العطلة</h3>
                        <p><span class="closed-day">الجمعة</span><br>مغلق</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="social-section">
        <div class="container">
            <h2>تابعنا على وسائل التواصل</h2>
            <div class="social-links">
                <!-- Primary Platforms -->
                <a href="https://wa.me/+9647878436643" class="social-link whatsapp whatsapp-link" target="_blank" title="واتساب">
                    <i class="fab fa-whatsapp"></i>
                </a>
                <a href="#" class="social-link facebook facebook-link" target="_blank" title="فيسبوك" style="display: none;">
                    <i class="fab fa-facebook-f"></i>
                </a>
                <a href="#" class="social-link instagram instagram-link" target="_blank" title="إنستغرام" style="display: none;">
                    <i class="fab fa-instagram"></i>
                </a>
                <a href="#" class="social-link telegram telegram-link" target="_blank" title="تيليغرام" style="display: none;">
                    <i class="fab fa-telegram-plane"></i>
                </a>

                <!-- Video Platforms -->
                <a href="#" class="social-link tiktok tiktok-link" target="_blank" title="تيك توك" style="display: none;">
                    <i class="fab fa-tiktok"></i>
                </a>
                <a href="#" class="social-link youtube youtube-link" target="_blank" title="يوتيوب" style="display: none;">
                    <i class="fab fa-youtube"></i>
                </a>
                <a href="#" class="social-link snapchat snapchat-link" target="_blank" title="سناب شات" style="display: none;">
                    <i class="fab fa-snapchat-ghost"></i>
                </a>

                <!-- Professional Platforms -->
                <a href="#" class="social-link twitter twitter-link" target="_blank" title="تويتر/X" style="display: none;">
                    <i class="fab fa-twitter"></i>
                </a>
                <a href="#" class="social-link linkedin linkedin-link" target="_blank" title="لينكد إن" style="display: none;">
                    <i class="fab fa-linkedin-in"></i>
                </a>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>معلومات التواصل</h3>
                    <p><i class="fas fa-map-marker-alt"></i> <span class="business-address">الكرادة، قرب مطعم المحطة</span></p>
                    <p><i class="fas fa-phone"></i> <span class="business-phone">+9647878436643</span></p>
                    <p><i class="fas fa-envelope"></i> <span class="business-email"><EMAIL></span></p>
                </div>
                <div class="footer-section">
                    <h3>أوقات العمل</h3>
                    <p><span class="working-days">السبت - الخميس</span>: <span class="working-hours">10 صباحاً - 5 مساءً</span></p>
                    <p><span class="closed-day">الجمعة</span>: مغلق</p>
                </div>
                <div class="footer-section">
                    <h3>تابعنا على وسائل التواصل</h3>
                    <div class="social-links">
                        <a href="#" class="whatsapp-link" target="_blank" title="واتساب" style="color: #25d366;" aria-label="تواصل معنا عبر واتساب">
                            <i class="fab fa-whatsapp" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="facebook-link" target="_blank" title="فيسبوك" style="color: #1877f2; display: none;" aria-label="تابعنا على فيسبوك">
                            <i class="fab fa-facebook-f" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="instagram-link" target="_blank" title="إنستغرام" style="color: #e4405f; display: none;" aria-label="تابعنا على إنستغرام">
                            <i class="fab fa-instagram" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="twitter-link" target="_blank" title="تويتر" style="color: #1da1f2; display: none;" aria-label="تابعنا على تويتر">
                            <i class="fab fa-twitter" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="telegram-link" target="_blank" title="تيليجرام" style="color: #0088cc; display: none;" aria-label="تابعنا على تيليجرام">
                            <i class="fab fa-telegram-plane" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="linkedin-link" target="_blank" title="لينكد إن" style="color: #0077b5; display: none;" aria-label="تابعنا على لينكد إن">
                            <i class="fab fa-linkedin-in" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="tiktok-link" target="_blank" title="تيك توك" style="color: #ff0050; display: none;" aria-label="تابعنا على تيك توك">
                            <i class="fab fa-tiktok" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="youtube-link" target="_blank" title="يوتيوب" style="color: #ff0000; display: none;" aria-label="تابعنا على يوتيوب">
                            <i class="fab fa-youtube" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="snapchat-link" target="_blank" title="سناب شات" style="color: #fffc00; display: none;" aria-label="تابعنا على سناب شات">
                            <i class="fab fa-snapchat-ghost" aria-hidden="true"></i>
                        </a>
                    </div>
                    <div style="margin-top: 1.5rem;">
                        <p><a href="terms.html"><i class="fas fa-file-contract"></i>الشروط والأحكام</a></p>
                        <p><a href="terms.html"><i class="fas fa-shield-alt"></i>سياسة الخصوصية</a></p>
                        <p><a href="terms.html"><i class="fas fa-undo-alt"></i>سياسة الإرجاع</a></p>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; <span class="business-name" data-setting="business_name">Care</span>. <span class="copyright-text" data-setting="copyright_text">جميع الحقوق محفوظة</span>.</p>
            </div>
        </div>
    </footer>

    <!-- Shared Supabase Configuration (must load first) -->
    <script src="js/supabase-config.js"></script>

    <!-- Site Settings Script -->
    <script src="js/site-settings.js"></script>

    <script>
        // Cart functionality
        let cart = JSON.parse(localStorage.getItem('cart')) || [];

        function updateCartCount() {
            const cartCount = document.getElementById('cartCount');
            const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
            cartCount.textContent = totalItems;
        }

        // Contact form functionality
        function showMessage(type, message) {
            const successDiv = document.getElementById('successMessage');
            const errorDiv = document.getElementById('errorMessage');

            // Ensure elements exist
            if (!successDiv || !errorDiv) {
                console.error('Message elements not found');
                return;
            }

            // Hide both messages first
            successDiv.classList.remove('show');
            errorDiv.classList.remove('show');

            // Force immediate hide
            setTimeout(() => {
                successDiv.style.display = 'none';
                errorDiv.style.display = 'none';
            }, 10);

            // Show the appropriate message after a brief delay
            setTimeout(() => {
                if (type === 'success') {
                    if (message) successDiv.textContent = message;
                    successDiv.style.display = 'flex';
                    // Force reflow
                    successDiv.offsetHeight;
                    successDiv.classList.add('show');

                    // Scroll to message
                    successDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
                } else {
                    if (message) errorDiv.textContent = message;
                    errorDiv.style.display = 'flex';
                    // Force reflow
                    errorDiv.offsetHeight;
                    errorDiv.classList.add('show');

                    // Scroll to message
                    errorDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            }, 50);

            // Hide message after 5 seconds
            setTimeout(() => {
                successDiv.classList.remove('show');
                errorDiv.classList.remove('show');
                setTimeout(() => {
                    successDiv.style.display = 'none';
                    errorDiv.style.display = 'none';
                }, 300); // Allow fade out animation
            }, 5000);
        }

        function generateWhatsAppMessage(formData) {
            const name = formData.get('name');
            const phone = formData.get('phone');
            const email = formData.get('email') || 'غير محدد';
            const subject = formData.get('subject');
            const message = formData.get('message');
            
            const subjectMap = {
                'product_inquiry': 'استفسار عن منتج',
                'order_status': 'حالة الطلب',
                'complaint': 'شكوى',
                'suggestion': 'اقتراح',
                'other': 'أخرى'
            };
            
            let whatsappMessage = `📞 *رسالة جديدة من موقع Care*\n\n`;
            whatsappMessage += `👤 *الاسم:* ${name}\n`;
            whatsappMessage += `📱 *الهاتف:* ${phone}\n`;
            whatsappMessage += `📧 *البريد:* ${email}\n`;
            whatsappMessage += `📋 *الموضوع:* ${subjectMap[subject] || subject}\n\n`;
            whatsappMessage += `💬 *الرسالة:*\n${message}`;
            
            return encodeURIComponent(whatsappMessage);
        }

        async function handleContactForm(event) {
            event.preventDefault();
            
            const formData = new FormData(event.target);
            const submitBtn = document.getElementById('submitBtn');
            
            // Validate required fields
            const name = formData.get('name').trim();
            const phone = formData.get('phone').trim();
            const subject = formData.get('subject');
            const message = formData.get('message').trim();
            
            if (!name || !phone || !subject || !message) {
                console.log('Validation failed - showing error message...');
                showMessage('error', 'يرجى ملء جميع الحقول المطلوبة');
                return;
            }
            
            // Show loading
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإرسال...';
            
            try {
                // Save to Supabase contact_submissions table
                const { data, error } = await supabase
                    .from('contact_submissions')
                    .insert([
                        {
                            name: name,
                            email: formData.get('email') || null,
                            phone: phone,
                            subject: subject,
                            message: message
                        }
                    ]);

                if (error) {
                    console.error('Supabase error:', error);
                    // Continue with WhatsApp even if database save fails
                }

                // Generate WhatsApp message
                const whatsappMessage = generateWhatsAppMessage(formData);
                const whatsappUrl = `https://wa.me/+9647878436643?text=${whatsappMessage}`;

                // Open WhatsApp
                window.open(whatsappUrl, '_blank');

                // Show success message
                console.log('Showing success message...');
                showMessage('success', 'تم إرسال رسالتك بنجاح! سيتم توجيهك إلى واتساب.');

                // Reset form
                event.target.reset();
                
            } catch (error) {
                console.error('Error sending message:', error);
                console.log('Showing error message...');
                showMessage('error', 'حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى.');
            } finally {
                // Reset button
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-paper-plane"></i> إرسال الرسالة';
            }
        }







        // Test message display function (for debugging)
        function testMessages() {
            console.log('Testing success message...');
            showMessage('success', 'رسالة نجاح تجريبية');

            setTimeout(() => {
                console.log('Testing error message...');
                showMessage('error', 'رسالة خطأ تجريبية');
            }, 6000);
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            updateCartCount();

            // Contact form submission
            const contactForm = document.getElementById('contactForm');
            contactForm.addEventListener('submit', handleContactForm);

            // Phone number formatting
            document.getElementById('phone').addEventListener('input', function(e) {
                let value = e.target.value.replace(/\D/g, '');
                if (value.length > 11) {
                    value = value.substring(0, 11);
                }
                e.target.value = value;
            });
        });
    </script>
</body>
</html>
